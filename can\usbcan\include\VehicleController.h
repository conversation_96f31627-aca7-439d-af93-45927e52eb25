#include <vector>
#include <limits>
#include <usbcan/UsbCan.h>
#include <unordered_map>
#include <thread>
#include <mutex>
#include <chrono>
#include <ctime>
#include <algorithm>
#include <std_msgs/String.h>
#include <cmath>
#include <fstream>
#include <iostream>
#include <string>
#include <sstream>
#include <map>
#include <iomanip>
#include <nlohmann/json.hpp>

#include <auto_msgs/ControlCommandStamped.h>
#include <auto_msgs/MissionState.h>
#include <auto_msgs/TopicState.h>
#include <tf/transform_broadcaster.h>
#include <tf/transform_listener.h>
#include <tf/transform_datatypes.h>
#include <tf2/utils.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <ros/package.h>
#include <ros/ros.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/UInt8MultiArray.h>
#include <std_msgs/Int8MultiArray.h>
#include <std_msgs/UInt8.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Char.h>
#include <std_msgs/Int32.h>
#include <geometry_msgs/PoseStamped.h>
#include <boost/math/constants/constants.hpp>
#include <usbcan_msgs/WheelsEncoder.h>
#include <usbcan_msgs/gps.h>
#include <usbcan/can_msg.h>
#include <usbcan_msgs/steering_angle.h>
#include <ros/timer.h>
#include <tf/tf.h>
#include <tf/transform_datatypes.h>
#include "djyxlogger.h"
#include "ReverseAdjustmentModule.h"

class VehicleController
{
public:
    // 构造函数
    VehicleController(ros::NodeHandle &nh, djyx::Logger &logger) : nh_(nh), logger_(logger), Mission_Stop(false), error_elimination(true), isblock(false), Safety(false), isTooFar(false), Ultrasonic_stop(false),
                                                                   button_pre(-1), flag(0), red_light_flag(false), Hooking(false), Ultrasonic_send_flag(0), associate_arrived(false),
                                                                   Auto_Hook(false), is_stopped(false), have_associate(false), time_count_Hook(false), is_BatteryLow(false), time_count(false),
                                                                   topic_stop(false), located_flag(false), Hookup_flag(false), Arrived(false), has_stopped(false), Voice_flag(true), accept_messages_(false),
                                                                   current_goal_reverse_flag(false), obstacle_detected(false), current_goal_reverse_direction("left"), reverse_module_(logger),
                                                                   timer(nh_.createTimer(ros::Duration(0.1), [this](const ros::TimerEvent &event)
                                                                                         { Event_Timer(event); }))
    {
        forward_speed = 0.0;
        retreat_speed = 0.0;
        left_angle = 0.0;
        right_angle = 0.0;
        x_goal = 0.0;
        y_goal = 0.0;
        Error_Info.data.resize(10, 0);

        try
        {
            // 读取目标位置的Json文件
            goals_ = ReadJsonFile();
            ROS_INFO("[DEBUG] Loaded %zu goals from JSON file", goals_.size());
            for (const auto &goals : goals_)
            {
                goal_map[goals.name] = goals;
                ROS_INFO("[DEBUG] Added goal: %s (%.2f, %.2f)",
                         goals.name.c_str(), goals.position_x, goals.position_y);
            }
            ROS_INFO("[DEBUG] Total goals in goal_map: %zu", goal_map.size());
        }
        catch (const std::exception &e)
        {
            // 处理异常情况
            std::cerr << "Failed to read goals_ from file: " << e.what() << std::endl;
            ROS_ERROR("[DEBUG] Failed to load goals from JSON file: %s", e.what());
        }

        // 订阅与发布赋值，定义在下面
        // 发布者
        wheels_pub = nh_.advertise<usbcan_msgs::WheelsEncoder>("/wheels", 10);
        speed_pub = nh_.advertise<std_msgs::Float32>("/speed", 10);
        goal_pub = nh_.advertise<geometry_msgs::PoseStamped>("/move_base_simple/goal", 1);
        // Ultrasonic_pub = nh_.advertise<usbcan_msgs::WheelsEncoder>("/Ultrasonic_distance", 10);
        soc_pub = nh_.advertise<std_msgs::UInt8>("/bsm_soc", 10);
        error_info_pub = nh_.advertise<std_msgs::UInt8MultiArray>("/error_info", 10);
        reverse_status_pub = nh_.advertise<std_msgs::Bool>("/reverse_status", 10);

        // 订阅者
        ctrl_sub = nh_.subscribe("/ctrl_cmd", 10, &VehicleController::ctrlCmdCallback, this);                         // 控制命令的消息
        status_sub = nh_.subscribe("/ndt_monitor/ndt_status", 10, &VehicleController::NDTStatusCallback, this);       // NDT匹配状态信息
        block_sub = nh_.subscribe("/isblock", 10, &VehicleController::IsBlockCallback, this);                         // 是否遇到障碍物的信息op
        pose_sub = nh_.subscribe("/current_pose", 10, &VehicleController::PoseCallback, this);                        // 当前的位姿信息
        toofar_sub = nh_.subscribe("/is_too_far", 10, &VehicleController::IsTooFarCallback, this);                    // 是否遇到障碍物的信息
        topic_state_sub = nh_.subscribe("/topic_state", 10, &VehicleController::TopicStateCallback, this);            // 订阅话题检测
        obstacle_detected_sub = nh_.subscribe("/obstacle_detected", 10, &VehicleController::SafetyBoxCallback, this);// 安全框检测检测
        obstacle_waypoint_sub = nh_.subscribe("/obstacle_waypoint", 10, &VehicleController::ObstacleWaypointCallback, this); // 是否遇到障碍物的信息a*
        force_reverse_sub = nh_.subscribe("/force_reverse_request", 10, &VehicleController::ForceReverseCallback, this); // 强制倒车请求
        
        // 定期检查话题健康状态
        health_timer_ = nh_.createTimer(ros::Duration(1.0), &VehicleController::checkHealth, this);

        // 加载CAN记录配置并初始化CAN数据记录功能
        // loadCanLoggingConfig();
        // initCanLogging();

        // 创建CAN数据记录定时器
        can_log_timer_ = nh_.createTimer(ros::Duration(can_log_interval_sec_), &VehicleController::rotateLogFile, this);
    };
    // 析构函数
    ~VehicleController()
    {
        // 停止所有定时器
        timer.stop();
        health_timer_.stop();
        can_log_timer_.stop();

        // 关闭CAN日志文件
        closeCurrentLogFile();
    }

    void ProcessLoop(); // 处理循环，替代原有的 process_loop 函数

    void fakeCanMsgWheels(); // CAN数据下发函数，发送控车数据

private:
    using json = nlohmann::json;

    struct Associate_Goal
    {
        std::string name;
        double position_x, position_y, position_z;
        double orientation_x, orientation_y, orientation_z, orientation_w;
    };

    struct Goal
    {
        std::string name;
        double position_x, position_y, position_z;
        double orientation_x, orientation_y, orientation_z, orientation_w;
        bool has_associate, decoupling, need_reverse; // need_reverse为倒车标志
        std::string reverse_direction; // 倒车方向："left"或"right"，默认"left"
        Associate_Goal associate_goal;
    };
    // 私有成员变量，原先的全局变量
    ros::NodeHandle nh_;
    djyx::Logger &logger_;

    ros::Timer health_timer_; // 健康检查定时器
    ros::Time last_received_; // 话题的最后接收消息时间

    std::unordered_map<std::string, Goal> goal_map;

    std::mutex mutex_;

    geometry_msgs::PoseStamped goal;
    geometry_msgs::PoseStamped associate_goal;

    // 发布
    ros::Publisher wheels_pub, speed_pub, goal_pub, Ultrasonic_pub, soc_pub, error_info_pub, reverse_status_pub;

    // 订阅
    ros::Subscriber ctrl_sub, status_sub, block_sub, pose_sub, toofar_sub, topic_state_sub, obstacle_detected_sub, obstacle_waypoint_sub, force_reverse_sub;

    // 用于通信和控制的对象
    UsbCan usbCan;

    // 倒车调整模块
    ReverseAdjustmentModule reverse_module_;

    int button_pre, flag, Ultrasonic_send_flag;

    double wheel_speed, distance_goal; // distance_goal:车辆与目标点的距离

    bool red_light_flag, Hookup_flag, ControlMode, is_BatteryLow, Mission_Stop, Ultrasonic_stop, topic_flag, Safety,
        time_count, time_count_Hook, isblock, isTooFar, have_associate, associate_arrived, error_elimination,
        Auto_Hook, located_flag, Hooking, Arrived, is_stopped, has_stopped, accept_messages_, topic_stop, Voice_flag,
        current_goal_reverse_flag, // 当前目标点倒车标志
        obstacle_detected; // 障碍物检测状态

    std::string current_goal_reverse_direction; // 当前目标点倒车方向

    char Gear = 0x22, EPB = 0x00, Light_flag = 0x00, Hook = 0x20; // Gear:档位  EPB:电子驻车  Light_flag:转向灯  Hook:自动托钩

    double left_angle, right_angle, forward_speed,
        retreat_speed, x_goal, y_goal;

    string ndt_status, topic, block_topic_;

    ros::Time start_time, start_time_Hook, stop_time_start_;
    ros::Timer timer;

    std::vector<std::string> error_messages_;
    std_msgs::Bool Make_newplan;

    // CAN数据结构，包含数据和时间戳
    struct CanDataEntry
    {
        std::string data;
        ros::Time timestamp;

        CanDataEntry() = default;
        CanDataEntry(const std::string &d, const ros::Time &t) : data(d), timestamp(t) {}
    };

    std::map<INT, CanDataEntry> canDataMap;
    std::map<std::string, std::chrono::system_clock::time_point> last_logged_times;
    std::vector<Goal> goals_;

    // CAN数据记录相关成员变量
    std::string can_log_directory_;    // CAN数据日志目录
    std::ofstream current_log_file_;   // 当前日志文件流
    std::string current_log_filename_; // 当前日志文件名
    ros::Timer can_log_timer_;         // CAN数据记录定时器
    std::mutex can_log_mutex_;         // CAN日志文件互斥锁

    // CAN数据记录配置参数
    int can_log_interval_sec_;                // 日志文件切换间隔(秒)
    int can_cleanup_interval_sec_;            // 清理检查间隔(秒)
    int can_log_retention_days_;              // 日志文件保留天数
    bool enable_logging_;                     // 是否启用CAN数据记录
    bool enable_real_time_flush_;             // 是否启用实时刷新
    int max_log_file_size_mb_;                // 单个日志文件最大大小(MB)
    std::string log_file_prefix_;             // 日志文件名前缀
    std::vector<uint32_t> can_ids_to_log_;    // 需要记录的CAN ID列表
    std::vector<uint32_t> can_ids_to_filter_; // 需要过滤的CAN ID列表

    std_msgs::UInt8MultiArray Error_Info;
    std_msgs::UInt8 soc;

    // 计时器相关常量
    static constexpr double SPEED_THRESHOLD = 0.05;      // 统一车速阈值 (m/s)
    static constexpr double RED_LIGHT_TIMEOUT = 30.0;    // 红灯警告超时时间 (s)
    static constexpr double HOOKUP_TIMEOUT = 5.0;        // 脱钩超时时间 (s)
    static constexpr double MAX_TIMER_DURATION = 3600.0; // 最大计时时长 (s)

    // 私有成员函数
    void Event_Timer(const ros::TimerEvent &event);
    void handleRedLightTimer(const ros::Time &now); // 处理红灯警告计时器
    void handleHookupTimer(const ros::Time &now);   // 处理自动脱钩计时器

    // 数据处理函数
    void HandleCanObject();                                           // CAN
    void HandleBodyFaultMessage(const VCI_CAN_OBJ &can_obj);          // 车身故障
    void HandleButtonMessage(const VCI_CAN_OBJ &can_obj);             // 按钮
    void HandleButtonMessage2(const VCI_CAN_OBJ &can_obj);            // 按钮2
    void HandleVehicleControlModeMessage(const VCI_CAN_OBJ &can_obj); // 控制模式反馈
    void HandleBmsMessage(const VCI_CAN_OBJ &can_obj);                // 电池反馈
    void HandleSpeedMessage(const VCI_CAN_OBJ &can_obj);              // 车速反馈

    // 回调函数
    void PoseCallback(const geometry_msgs::PoseStampedConstPtr &msg);
    void ctrlCmdCallback(const auto_msgs::ControlCommandStampedConstPtr &msg);
    void TopicStateCallback(const auto_msgs::TopicState::ConstPtr &msg);
    void NDTStatusCallback(const std_msgs::StringConstPtr &msg)
    {
        ndt_status = msg->data;

        located_flag = ndt_status != "NDT_OK" ? true : false;
    }
    void IsBlockCallback(const std_msgs::Bool::ConstPtr &msg)
    {
        isblock = msg->data;
    }
    void IsTooFarCallback(const std_msgs::Bool::ConstPtr &msg)
    {
        isTooFar = msg->data;
    }
    void SafetyBoxCallback(const std_msgs::Bool::ConstPtr &msg)
    {
        Safety = msg->data;
    }
    void ObstacleWaypointCallback(const std_msgs::Int32::ConstPtr &msg)
    {
        obstacle_detected = (msg->data >= 0); // 如果waypoint索引>=0，说明检测到障碍物
    }
    void ForceReverseCallback(const std_msgs::Bool::ConstPtr &msg);

    // CAN设备状态检查
    bool checkCanDeviceStatus();

    void PublishErrorInfo(const int info_num, const int index)
    {
        Error_Info.data.clear();
        Error_Info.data.resize(10, 0);
        Error_Info.data[index] = info_num;
        error_info_pub.publish(Error_Info);
    }

    // Tools函数
    // --------------------------------文件函数-------------------------------------------
    std::vector<Goal> ReadJsonFile()
    {
        std::vector<Goal> goals;

        Goal goal;

        std::string package_path = ros::package::getPath("ins");
        std::string filepath = package_path + "/config/line.json";
        std::ifstream file(filepath);
        if (!file.is_open())
        {
            throw std::runtime_error("Cannot open file:" + filepath);
        }

        json j;
        file >> j;
        goals.reserve(j.size());

        for (const auto &item : j)
        {
            goal.name = item["name"];
            goal.position_x = item["position_x"];
            goal.position_y = item["position_y"];
            goal.position_z = item["position_z"];
            goal.orientation_x = item["orientation_x"];
            goal.orientation_y = item["orientation_y"];
            goal.orientation_z = item["orientation_z"];
            goal.orientation_w = item["orientation_w"];
            goal.need_reverse = (item["need_reverse"] == "yes"); // 添加倒车标志解析
            goal.decoupling = (item["decoupling"] == "yes");
            goal.has_associate = (item["associate"] == "yes");

            // 添加倒车方向解析，默认为"left"
            if (item.contains("reverse_direction")) {
                std::string direction = item["reverse_direction"];
                goal.reverse_direction = (direction == "right") ? "right" : "left";
            } else {
                goal.reverse_direction = "left"; // 默认向左
            }

            if (goal.has_associate)
            {
                goal.associate_goal.name = item["name"];
                goal.associate_goal.position_x = item["associate_position_x"];
                goal.associate_goal.position_y = item["associate_position_y"];
                goal.associate_goal.position_z = item["associate_position_z"];
                goal.associate_goal.orientation_x = item["associate_orientation_x"];
                goal.associate_goal.orientation_y = item["associate_orientation_y"];
                goal.associate_goal.orientation_z = item["associate_orientation_z"];
                goal.associate_goal.orientation_w = item["associate_orientation_w"];
            }
            goals.push_back(goal);
        }
        return goals;
    }
    //--------------------------------------调试函数--------------------------------------------
    // 打印CAN
    void PrintCANData(const VCI_CAN_OBJ &can)
    {
        // 打印报文ID和数据
        std::cout << "Received CAN message with ID: " << std::hex << can.ID;
        std::cout << " Data: ";
        for (int i = 0; i < can.DataLen; ++i)
        {
            // 打印数据
            std::cout << std::hex << static_cast<int>(can.Data[i]) << " ";
        }
        std::cout << std::endl;
        // std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待500毫秒
        // lastPrintTime = now; // 更新上次打印时间
    }

    // 更新CAN数据
    void updateCanData(const VCI_CAN_OBJ &can_obj)
    {
        std::stringstream dataStream;
        for (int i = 0; i < can_obj.DataLen; ++i)
        {
            dataStream << std::setfill('0') << std::setw(2) << std::hex << static_cast<int>(can_obj.Data[i]) << " ";
        }

        // 创建包含时间戳的CAN数据条目
        canDataMap[can_obj.ID] = CanDataEntry(dataStream.str(), ros::Time::now());

        // 实时记录CAN数据到日志文件(如果启用且该ID需要记录)
        if (enable_logging_ && shouldLogCanId(can_obj.ID))
        {
            logCanDataToFile(can_obj);
        }
    }

    // CAN数据记录和清理相关函数
    void loadCanLoggingConfig();                       // 加载CAN记录配置
    void initCanLogging();                             // 初始化CAN数据记录
    void logCanDataToFile(const VCI_CAN_OBJ &can_obj); // 记录CAN数据到文件
    void createNewLogFile();                           // 创建新的日志文件
    void rotateLogFile(const ros::TimerEvent &event);  // 定时切换日志文件
    void cleanupOldLogFiles();                         // 清理旧日志文件
    std::string generateLogFileName() const;           // 生成日志文件名
    void closeCurrentLogFile();                        // 关闭当前日志文件
    bool shouldLogCanId(uint32_t can_id) const;        // 判断是否应该记录该CAN ID
    size_t getCurrentLogFileSize() const;              // 获取当前日志文件大小(字节)
    bool checkAndRotateLogFileBySize();                // 检查文件大小并根据需要切换文件
    //--------------------------------------------------------------------------------------------

    //--------------------------------------工具函数--------------------------------------------
    /**
     * @brief 字节位数检查函数
     *
     * @param number 要检查的数字
     */
    int BitPosition(unsigned int number)
    {
        if (number == 0 || number > 16) {
            ROS_WARN("[DEBUG] BitPosition: invalid number %d (must be 1-16)", number);
            return -1;
        }

        // 检查是否是2的幂次
        if ((number & (number - 1)) != 0) {
            ROS_WARN("[DEBUG] BitPosition: number %d is not a power of 2", number);
            // 对于非2的幂次，我们需要找到最高位
            int pos = 0;
            unsigned int temp = number;
            while (temp > 1) {
                temp >>= 1;
                pos++;
            }
            ROS_INFO("[DEBUG] BitPosition: using highest bit position %d for number %d", pos, number);
            return pos;
        }

        int result = static_cast<int>(std::log2(number));

        // 使用慢速输出避免刷屏
        static auto last_bitpos_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_bitpos_time);

        if (elapsed.count() >= 3) {
            printf("🔢 BitPosition: %d -> %d\n", number, result);
            fflush(stdout);
            last_bitpos_time = current_time;
        }

        return result;
    }

    tf::Vector3 point2vector(geometry_msgs::Point point)
    {
        tf::Vector3 vector(point.x, point.y, point.z);
        return vector;
    }

    /**
     * @brief 计算两点之间的距离
     *
     * @param point1 点1
     * @param point2 点2
     *
     * @return double 两点之间的距离
     */
    double calculateDistance(const geometry_msgs::Point &point1, const geometry_msgs::Point &point2)
    {
        return sqrt(pow(point2.x - point1.x, 2) + pow(point2.y - point1.y, 2));
    }

    /**
     * 延时函数
     * @param seconds 延时的秒数，可以是浮点数
     */
    void delay(double seconds)
    {
        ros::Duration(seconds).sleep();
    }

    /**
     * @brief 定期检查每个话题的健康状态
     */
    void checkHealth(const ros::TimerEvent &)
    {
        ros::Time now = ros::Time::now();
        if ((now - last_received_).toSec() > 5)
        {
            topic_flag = false;
        }
        else
        {
            topic_flag = true;
        }
    }
    //--------------------------------------------------------------------------------------------
};