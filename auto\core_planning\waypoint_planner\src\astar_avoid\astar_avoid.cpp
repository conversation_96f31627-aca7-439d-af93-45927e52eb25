/*
 * Copyright 2015-2019 Autoware Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "waypoint_planner/astar_avoid/astar_avoid.h"

AstarAvoid::AstarAvoid()
    : nh_(),
      private_nh_("~"),
      closest_waypoint_index_(-1),
      obstacle_waypoint_index_(-1),
      closest_local_index_(-1),
      costmap_initialized_(false),
      current_pose_initialized_(false),
      current_velocity_initialized_(false),
      base_waypoints_initialized_(false),
      closest_waypoint_initialized_(false),
      terminate_thread_(false)
{
  private_nh_.param<bool>("enable_avoidance", enable_avoidance_, false);
  // 设置安全路径点的数量
  private_nh_.param<int>("safety_waypoints_size", safety_waypoints_size_, 100);
  // 设置节点的更新频率（单位：Hz）
  private_nh_.param<double>("update_rate", update_rate_, 10.0);

  // 设置避障时路径点的目标速度（单位：km/h）
  private_nh_.param<double>("avoid_waypoints_velocity", avoid_waypoints_velocity_, 10.0);
  // 设置触发避障模式的速度阈值（单位：km/h）
  private_nh_.param<double>("avoid_start_velocity", avoid_start_velocity_, 5.0);
  // 设置路径重规划的时间间隔（单位：秒）
  private_nh_.param<double>("replan_interval", replan_interval_, 2.0);

  // 设置在路径搜索时考虑的路径点数量
  private_nh_.param<int>("search_waypoints_size", search_waypoints_size_, 50);
  // 设置搜索路径点时的步进大小
  private_nh_.param<int>("search_waypoints_delta", search_waypoints_delta_, 2);
  // 设置查找最近路径点的搜索窗口大小
  private_nh_.param<int>("closest_search_size", closest_search_size_, 30);

  // 设置启用避障操作的最小路径点索引
  private_nh_.param<int>("min_waypoint_index", min_waypoint_index_, 0);
  // 设置启用避障操作的最大路径点索引
  private_nh_.param<int>("max_waypoint_index", max_waypoint_index_, 0);

  // 设置话题订阅和发布
  safety_waypoints_pub_ = nh_.advertise<auto_msgs::Lane>("safety_waypoints", 1, true);
  costmap_sub_ = nh_.subscribe("costmap", 1, &AstarAvoid::costmapCallback, this);
  current_pose_sub_ = nh_.subscribe("current_pose", 1, &AstarAvoid::currentPoseCallback, this);
  current_velocity_sub_ = nh_.subscribe("current_velocity", 1, &AstarAvoid::currentVelocityCallback, this);
  base_waypoints_sub_ = nh_.subscribe("base_waypoints", 1, &AstarAvoid::baseWaypointsCallback, this);
  closest_waypoint_sub_ = nh_.subscribe("closest_waypoint", 1, &AstarAvoid::closestWaypointCallback, this);
  obstacle_waypoint_sub_ = nh_.subscribe("obstacle_waypoint", 1, &AstarAvoid::obstacleWaypointCallback, this);

  // 创建ROS定时器，以固定频率执行循环操作
  rate_ = new ros::Rate(update_rate_);
}

AstarAvoid::~AstarAvoid()
{
  // 在析构函数中，确保发布线程被正确地关闭
  publish_thread_.join();
}

void AstarAvoid::costmapCallback(const nav_msgs::OccupancyGrid &msg)
{
  costmap_ = msg;
  tf::poseMsgToTF(costmap_.info.origin, local2costmap_);
  costmap_initialized_ = true;
}

void AstarAvoid::currentPoseCallback(const geometry_msgs::PoseStamped &msg)
{
  current_pose_global_ = msg;

  if (!enable_avoidance_)
  {
    current_pose_initialized_ = true;
  }
  else
  {
    current_pose_local_.pose = transformPose(
        current_pose_global_.pose, getTransform(costmap_.header.frame_id, current_pose_global_.header.frame_id));
    current_pose_local_.header.frame_id = costmap_.header.frame_id;
    current_pose_local_.header.stamp = current_pose_global_.header.stamp;
    current_pose_initialized_ = true;
  }
}

void AstarAvoid::currentVelocityCallback(const geometry_msgs::TwistStamped &msg)
{
  current_velocity_ = msg;
  current_velocity_initialized_ = true;
}

void AstarAvoid::baseWaypointsCallback(const auto_msgs::Lane &msg)
{
  static auto_msgs::Lane prev_base_waypoints;
  base_waypoints_ = msg;

  if (base_waypoints_initialized_)
  {
    // detect waypoint change by timestamp update
    ros::Time t1 = prev_base_waypoints.header.stamp;
    ros::Time t2 = base_waypoints_.header.stamp;
    if ((t2 - t1).toSec() > 1e-3)
    {
      ROS_INFO("Receive new /base_waypoints, reset waypoint index.");
      closest_local_index_ = -1; // reset local closest waypoint
      prev_base_waypoints = base_waypoints_;
    }
  }
  else
  {
    prev_base_waypoints = base_waypoints_;
  }

  base_waypoints_initialized_ = true;
}
// int min_waypoint_index,max_waypoint_index;
void AstarAvoid::closestWaypointCallback(const std_msgs::Int32 &msg)
{
  closest_waypoint_index_ = msg.data;
  // if (closest_waypoint_index_ > min_waypoint_index_ && closest_waypoint_index_ < max_waypoint_index_)
  // {
  //   enable_avoidance_ = false;
  // }
  // else
  // {
  //   enable_avoidance_ = true;
  // }
  if (closest_waypoint_index_ == -1)
  {
    closest_local_index_ = -1; // reset local closest waypoint
  }

  closest_waypoint_initialized_ = true;
}

void AstarAvoid::obstacleWaypointCallback(const std_msgs::Int32 &msg)
{
  obstacle_waypoint_index_ = msg.data;
}

void AstarAvoid::run()
{
  // 检查主题订阅是否完成，初始化状态
  state_ = AstarAvoid::STATE::INITIALIZING;

  // 在所有系统初始化完成之前循环检查
  while (ros::ok())
  {
    ros::spinOnce();        // 处理一次回调
    if (checkInitialized()) // 检查是否所有必要的订阅都已初始化
    {
      break;
    }
    // ROS_WARN("Waiting for subscribing topics..."); // 如果未初始化完成，打印警告消息
    ros::Duration(1.0).sleep();                    // 暂停1秒再次检查
  }

  // 主循环
  int end_of_avoid_index = -1;                           // 避障结束的路径点索引
  ros::WallTime start_plan_time = ros::WallTime::now();  // 记录规划开始时间
  ros::WallTime start_avoid_time = ros::WallTime::now(); // 记录避障开始时间

  // 重置障碍物索引
  obstacle_waypoint_index_ = -1;

  // 初始状态设置为基本路线传递
  state_ = AstarAvoid::STATE::RELAYING;

  // 启动路径点发布线程
  publish_thread_ = std::thread(&AstarAvoid::publishWaypoints, this);

  // ROS节点正常运行时执行循环
  while (ros::ok())
  {
    ros::spinOnce(); // 处理一次回调

    // 如果未启用避障模式，继续执行下一次循环
    if (!enable_avoidance_)
    {
      rate_->sleep();
      continue;
    }

    // 检测是否发现障碍物，以及当前速度是否小于启动避障的速度阈值
    bool found_obstacle = (obstacle_waypoint_index_ >= 0);
    bool avoid_velocity = (current_velocity_.twist.linear.x < avoid_start_velocity_ / 3.6);

    // 根据当前状态和条件更新状态
    if (state_ == AstarAvoid::STATE::RELAYING)
    {
      avoid_waypoints_ = base_waypoints_; // 使用基本路径点

      if (found_obstacle) // 如果发现障碍物，转换到停止状态
      {
        ROS_INFO("RELAYING -> STOPPING, Decelerate for stopping");
        state_ = AstarAvoid::STATE::STOPPING;
      }
    }
    else if (state_ == AstarAvoid::STATE::STOPPING)
    {
      bool replan = ((ros::WallTime::now() - start_plan_time).toSec() > replan_interval_);

      if (!found_obstacle) // 如果障碍物消失，回到传递模式
      {
        ROS_INFO("STOPPING -> RELAYING, Obstacle disappers");
        state_ = AstarAvoid::STATE::RELAYING;
      }
      else if (replan && avoid_velocity) // 如果满足重新规划条件，转到规划状态
      {
        ROS_INFO("STOPPING -> PLANNING, Start A* planning");
        state_ = AstarAvoid::STATE::PLANNING;
      }
    }
    else if (state_ == AstarAvoid::STATE::PLANNING)
    {
      start_plan_time = ros::WallTime::now();

      if (planAvoidWaypoints(end_of_avoid_index)) // 尝试规划避障路径
      {
        ROS_INFO("PLANNING -> AVOIDING, Found path");
        state_ = AstarAvoid::STATE::AVOIDING;
        start_avoid_time = ros::WallTime::now();
      }
      else // 如果规划失败，回到停止状态
      {
        ROS_INFO("PLANNING -> STOPPING, Cannot find path");
        state_ = AstarAvoid::STATE::STOPPING;
      }
    }
    else if (state_ == AstarAvoid::STATE::AVOIDING)
    {
      bool reached = (getLocalClosestWaypoint(avoid_waypoints_, current_pose_global_.pose, closest_search_size_) > end_of_avoid_index);
      if (reached) // 如果到达目标点，回到传递模式
      {
        ROS_INFO("AVOIDING -> RELAYING, Reached goal");
        state_ = AstarAvoid::STATE::RELAYING;
      }
      else if (found_obstacle && avoid_velocity) // 如果依旧有障碍物且需要重新规划
      {
        bool replan = ((ros::WallTime::now() - start_avoid_time).toSec() > replan_interval_);
        if (replan)
        {
          ROS_INFO("AVOIDING -> STOPPING, Abort avoiding");
          state_ = AstarAvoid::STATE::STOPPING;
        }
      }
    }

    rate_->sleep(); // 按设定的频率暂停
  }

  terminate_thread_ = true; // 结束发布线程
}

bool AstarAvoid::checkInitialized()
{
  bool initialized = false;

  // check for relay mode
  initialized = (current_pose_initialized_ && closest_waypoint_initialized_ && base_waypoints_initialized_ &&
                 (closest_waypoint_index_ >= 0));
  std::cout << "------------等待目标点中------------" << std::endl;
  
  // ROS_WARN("current_pose_initialized_: %s", current_pose_initialized_ ? "true" : "false");
  // ROS_WARN("closest_waypoint_initialized_: %s", closest_waypoint_initialized_ ? "true" : "false");
  // ROS_WARN("base_waypoints_initialized_: %s", base_waypoints_initialized_ ? "true" : "false");

  // check for avoidance mode, additionally
  if (enable_avoidance_)
  {
    initialized = (initialized && (current_velocity_initialized_ && costmap_initialized_));
  }

  return initialized;
}

// 规划避障路径
bool AstarAvoid::planAvoidWaypoints(int &end_of_avoid_index)
{
  // 初始状态假设找到路径
  bool found_path = true;
  // 获取当前位置最接近的路径点索引
  int closest_waypoint_index = getLocalClosestWaypoint(avoid_waypoints_, current_pose_global_.pose, closest_search_size_);

  // 以一定的增量更新目标位置，并执行A*搜索
  for (int i = search_waypoints_delta_; i < static_cast<int>(search_waypoints_size_); i += search_waypoints_delta_)
  {
    // 更新目标路径点索引
    int goal_waypoint_index = closest_waypoint_index + obstacle_waypoint_index_ + i;
    // 如果目标索引超出路径点数组大小，退出循环
    if (goal_waypoint_index >= static_cast<int>(avoid_waypoints_.waypoints.size()))
    {
      ROS_WARN("break!!!");
      break;
    }

    // 更新目标位置
    goal_pose_global_ = avoid_waypoints_.waypoints[goal_waypoint_index].pose;
    goal_pose_local_.header = costmap_.header;
    // 将全局坐标转换为本地成本地图坐标
    goal_pose_local_.pose = transformPose(goal_pose_global_.pose,
                                          getTransform(costmap_.header.frame_id, goal_pose_global_.header.frame_id));

    // 初始化A*搜索的成本地图
    astar_.initialize(costmap_);

    // 执行A*搜索
    ros::WallTime start = ros::WallTime::now();
    found_path = astar_.makePlan(current_pose_local_.pose, goal_pose_local_.pose);
    ros::WallTime end = ros::WallTime::now();

    static ros::Publisher pub = nh_.advertise<nav_msgs::Path>("debug", 1, true);
    // 发布路径用于调试

    if (found_path)
    {
      // 如果找到路径，发布并合并避障路径
      pub.publish(astar_.getPath());
      end_of_avoid_index = goal_waypoint_index;
      mergeAvoidWaypoints(astar_.getPath(), end_of_avoid_index);
      // 检查是否成功添加了路径点
      if (avoid_waypoints_.waypoints.size() > 0)
      {
        // ROS_INFO("Found GOAL at index = %d", goal_waypoint_index);
        astar_.reset();
        return true;
      }
      else
      {
        found_path = false;
      }
    }
    astar_.reset();
  }

  ROS_ERROR("Can't find goal..."); // 如果所有尝试后仍未找到路径，返回失败
  return false;
}

// 将计算出的避障路径合并到现有的路径中
void AstarAvoid::mergeAvoidWaypoints(const nav_msgs::Path &path, int &end_of_avoid_index)
{
  auto_msgs::Lane current_waypoints = avoid_waypoints_; // 保存当前的避障路径点

  // 使用互斥锁确保线程安全
  std::lock_guard<std::mutex> lock(mutex_);
  avoid_waypoints_.waypoints.clear(); // 清空当前的避障路径点

  // 添加起始索引之前的路径点
  int closest_waypoint_index = getLocalClosestWaypoint(current_waypoints, current_pose_global_.pose, closest_search_size_);
  for (int i = 0; i < closest_waypoint_index; ++i)
  {
    avoid_waypoints_.waypoints.push_back(current_waypoints.waypoints.at(i));
  }

  // 设置避障路径点
  for (const auto &pose : path.poses)
  {
    auto_msgs::Waypoint wp;
    wp.pose.header = avoid_waypoints_.header;                                                                      // 设置头信息
    wp.pose.pose = transformPose(pose.pose, getTransform(avoid_waypoints_.header.frame_id, pose.header.frame_id)); // 转换坐标系
    wp.pose.pose.position.z = current_pose_global_.pose.position.z;                                                // 设置高度为常数
    wp.twist.twist.linear.x = avoid_waypoints_velocity_ / 3.6;                                                     // 设置速度为常数
    avoid_waypoints_.waypoints.push_back(wp);                                                                      // 添加新的避障路径点
  }

  // 添加目标索引之后的路径点
  for (int i = end_of_avoid_index; i < static_cast<int>(current_waypoints.waypoints.size()); ++i)
  {
    avoid_waypoints_.waypoints.push_back(current_waypoints.waypoints.at(i));
  }

  // 更新合并后的路径点索引
  end_of_avoid_index = closest_waypoint_index + path.poses.size();
}

// 发布路径点
void AstarAvoid::publishWaypoints()
{
  auto_msgs::Lane current_waypoints; // 用于存储当前有效的路径点

  // 在没有终止信号的情况下循环
  while (!terminate_thread_)
  {
    // 根据当前的导航状态选择路径点
    switch (state_)
    {
    case AstarAvoid::STATE::RELAYING:
      // 如果是中继状态，使用基础路径点
      current_waypoints = base_waypoints_;
      break;
    case AstarAvoid::STATE::STOPPING:
      // 如果是停止状态，保持当前路径点不变
      break;
    case AstarAvoid::STATE::PLANNING:
      // 如果是规划状态，也保持当前路径点不变
      break;
    case AstarAvoid::STATE::AVOIDING:
      // 如果是避障状态，使用避障路径点
      current_waypoints = avoid_waypoints_;
      break;
    default:
      // 默认使用基础路径点
      current_waypoints = base_waypoints_;
      break;
    }

    auto_msgs::Lane safety_waypoints; // 创建一个新的路径点列表用于发布
    safety_waypoints.header = current_waypoints.header;
    safety_waypoints.increment = current_waypoints.increment;

    // 从最近的路径点开始，推送安全路径点的数量
    for (int i = 0; i < safety_waypoints_size_; ++i)
    {
      int index = getLocalClosestWaypoint(current_waypoints, current_pose_global_.pose, closest_search_size_) + i;
      if (index < 0 || static_cast<int>(current_waypoints.waypoints.size()) <= index)
      {
        break;
      }
      const auto_msgs::Waypoint &wp = current_waypoints.waypoints[index];
      safety_waypoints.waypoints.push_back(wp); // 将选中的路径点添加到发布列表
    }

    // 如果有路径点需要发布，发送它们
    if (safety_waypoints.waypoints.size() > 0)
    {
      safety_waypoints_pub_.publish(safety_waypoints);
    }

    rate_->sleep(); // 根据设定的频率休眠
  }
}

tf::Transform AstarAvoid::getTransform(const std::string &from, const std::string &to)
{
  tf::StampedTransform stf;
  try
  {
    tf_listener_.lookupTransform(from, to, ros::Time(0), stf);
  }
  catch (tf::TransformException ex)
  {
    ROS_ERROR("%s", ex.what());
  }
  return stf;
}

// 获取给定位置最接近的路径点索引
int AstarAvoid::getLocalClosestWaypoint(const auto_msgs::Lane &waypoints, const geometry_msgs::Pose &pose, const int &search_size)
{
  static auto_msgs::Lane local_waypoints;      // 存储局部路径点，围绕自车周围的路径点
  const int prev_index = closest_local_index_; // 上一次找到的最近路径点索引

  // 如果当前没有有效的最近路径点索引，就在所有路径点中搜索
  if (closest_local_index_ == -1)
  {
    closest_local_index_ = getClosestWaypoint(waypoints, pose); // 使用全局搜索找到最近的路径点
  }
  // 如果已有有效的索引，则在这个索引的附近进行局部搜索
  else
  {
    // 确定搜索的起始和结束索引，限制搜索区域在前后半径 search_size / 2 的范围内
    int start_index = std::max(0, prev_index - search_size / 2);
    int end_index = std::min(prev_index + search_size / 2, (int)waypoints.waypoints.size());
    auto start_itr = waypoints.waypoints.begin() + start_index;
    auto end_itr = waypoints.waypoints.begin() + end_index;
    local_waypoints.waypoints = std::vector<auto_msgs::Waypoint>(start_itr, end_itr); // 创建局部路径点的子集

    // 在局部路径点子集中找到最近的路径点
    closest_local_index_ = start_index + getClosestWaypoint(local_waypoints, pose);
  }

  return closest_local_index_; // 返回找到的最近路径点索引
}
