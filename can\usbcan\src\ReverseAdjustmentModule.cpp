#include "ReverseAdjustmentModule.h"
#include <cmath>
#include <iostream>
#include <iomanip>

ReverseAdjustmentModule::ReverseAdjustmentModule(djyx::Logger &logger)
    : logger_(logger), is_reversing_(false), adjustment_complete_(false), target_yaw_(0.0), start_time_(0.0), angle_precision_(6.0 * M_PI / 180.0) // 默认5度精度
      ,
      position_precision_(1.2) // 放宽位置精度到0.8米，提高效率
      ,
      adjustment_speed_(30.0) // 提高调整速度到30.0
      ,
      forward_start_speed_(50.0) // 前进启步速度
      ,
      reverse_start_speed_(40.0) // 倒车启步速度
      ,
      speed_switch_threshold_(0.5) // 速度切换阈值
      ,
      steering_angle_(600.0) // 增大基础转向角度到800
      ,
      switch_interval_(6.0) // 延长切换间隔到6秒，让每次移动更充分
      ,
      brake_force_(150.0) // 默认刹车力度（柔和刹车）
      ,
      max_move_distance_(2) // 增大单次移动最大距离到2.5米
      ,
      start_x_(0.0), start_y_(0.0), last_move_distance_(0.0), is_moving_forward_(true), last_control_mode_(false),
      move_start_time_(0.0), move_start_x_(0.0), move_start_y_(0.0),
      is_stopping_(false), stop_start_time_(0.0), wheel_speed_threshold_(0.1) // 0.1为停稳阈值
{
}

void ReverseAdjustmentModule::startReverseAdjustment(const geometry_msgs::Pose &current_pose,
                                                     double goal_x, double goal_y, const std::string& direction)
{
    if (is_reversing_)
    {
        // ROS_WARN("倒车调整已在进行中，忽略新的调整请求");
        return;
    }

    // 开始倒车调整过程
    is_reversing_ = true;
    adjustment_complete_ = false;
    start_time_ = ros::Time::now().toSec();

    // 设置倒车方向，默认为left
    reverse_direction_ = (direction == "right") ? "right" : "left";

    // 记录初始位置
    start_x_ = current_pose.position.x;
    start_y_ = current_pose.position.y;
    last_move_distance_ = 0.0;
    is_moving_forward_ = true;

    // 初始化移动状态
    move_start_time_ = ros::Time::now().toSec();
    move_start_x_ = start_x_;
    move_start_y_ = start_y_;
    is_stopping_ = false;

    // 获取当前车辆的yaw角度
    double current_yaw = getYawFromQuaternion(current_pose.orientation);

    // 根据方向计算目标yaw角度
    if (reverse_direction_ == "right") {
        // 向右转90度
        target_yaw_ = normalizeAngle(current_yaw - M_PI / 2.0);
    } else {
        // 向左转90度（默认）
        target_yaw_ = normalizeAngle(current_yaw + M_PI / 2.0);
    }

    std::cout << "[倒车调整] 开始倒车调整: 目标坐标=(" << std::fixed << std::setprecision(2)
              << goal_x << ", " << goal_y << "), 初始位置=(" << start_x_ << ", " << start_y_
              << "), 倒车方向=" << reverse_direction_ << ", 当前角度=" << (current_yaw * 180.0 / M_PI)
              << "度, 目标角度=" << (target_yaw_ * 180.0 / M_PI) << "度" << std::endl;
}

bool ReverseAdjustmentModule::updateReverseAdjustment(const geometry_msgs::Pose &current_pose,
                                                      double wheel_speed,
                                                      bool control_mode,
                                                      double &forward_speed,
                                                      double &retreat_speed,
                                                      double &left_angle,
                                                      double &right_angle,
                                                      char &gear)
{
    if (!is_reversing_ || adjustment_complete_)
    {
        return adjustment_complete_;
    }

    // 检查车辆控制模式，只有在开启控制模式时才进行倒车调整
    if (!control_mode)
    {
        // 控制模式状态变化时打印日志
        if (last_control_mode_ != control_mode)
        {
            std::cout << "[倒车调整] 车辆控制模式未开启，暂停倒车调整" << std::endl;
            last_control_mode_ = control_mode;
        }

        // 控制模式未开启，停止所有控制输出
        forward_speed = 0.0;
        retreat_speed = 0.0;
        left_angle = 0.0;
        right_angle = 0.0;
        gear = 0x22;  // N档
        return false;  // 继续等待控制模式开启
    }

    // 控制模式开启时的状态变化日志
    if (last_control_mode_ != control_mode)
    {
        std::cout << "[倒车调整] 车辆控制模式已开启，恢复倒车调整" << std::endl;
        last_control_mode_ = control_mode;
    }

    double current_yaw = getYawFromQuaternion(current_pose.orientation);
    double yaw_error = normalizeAngle(target_yaw_ - current_yaw);
    double abs_yaw_error = fabs(yaw_error);

    // 计算当前位置与初始位置的距离
    double current_x = current_pose.position.x;
    double current_y = current_pose.position.y;
    double position_error = sqrt(pow(current_x - start_x_, 2) + pow(current_y - start_y_, 2));

    // 检查是否完成调整（角度精度满足且位置偏移不超过允许范围）
    if (abs_yaw_error < angle_precision_ && position_error <= position_precision_)
    {
        adjustment_complete_ = true;
        is_reversing_ = false;
        forward_speed = 0.0;
        retreat_speed = 0.0;
        left_angle = 0.0;
        right_angle = 0.0;
        gear = 0x22; // N档

        std::cout << "[倒车调整] 倒车调整完成！最终角度=" << std::fixed << std::setprecision(2)
                  << (current_yaw * 180.0 / M_PI) << "度, 角度误差="
                  << (abs_yaw_error * 180.0 / M_PI) << "度, 位置偏移=" << std::setprecision(3)
                  << position_error << "米，进行刹车操作" << std::endl;

        // 倒车调整完成后进行刹车，防止车辆溜走
        gear = 0x22; // N档
        forward_speed = 0.0;
        retreat_speed = brake_force_; // 使用柔和的刹车力度
        left_angle = 0.0;
        right_angle = 0.0;

        return true;
    }

    // 获取当前时间（提前定义，供日志使用）
    double current_time = ros::Time::now().toSec();

    // 检查位置偏移是否过大，如果过大则需要回到原点
    if (position_error > position_precision_ * 2.0)
    {
        // 简化位置偏移日志：每5秒打印一次警告
        static double last_position_log_time = 0.0;
        if (current_time - last_position_log_time > 5.0) {
            std::cout << "[倒车调整] 位置偏移过大(" << std::setprecision(1) << position_error
                      << "m)，回归中" << std::endl;
            last_position_log_time = current_time;
        }

        // 计算回到初始位置的方向
        double dx = start_x_ - current_x;
        double dy = start_y_ - current_y;
        double distance_to_start = sqrt(dx * dx + dy * dy);

        if (distance_to_start > 0.1)
        {
            // 根据距离初始位置的方向决定移动方向
            double angle_to_start = atan2(dy, dx);
            double angle_diff = normalizeAngle(angle_to_start - current_yaw);

            // 选择更接近的方向（前进或后退）
            is_moving_forward_ = (fabs(angle_diff) < M_PI / 2);
        }
    }

    // 新的智能移动控制逻辑

    // 计算当前移动的时间和距离
    double move_elapsed_time = current_time - move_start_time_;
    double move_distance = sqrt(pow(current_x - move_start_x_, 2) + pow(current_y - move_start_y_, 2));

    // 检查是否满足单次移动的最小条件：时间 >= switch_interval_ AND 距离 >= max_move_distance_
    bool time_condition_met = move_elapsed_time >= switch_interval_;
    bool distance_condition_met = move_distance >= max_move_distance_;
    bool should_stop_move = time_condition_met && distance_condition_met;

    // 检查是否需要停车和切换方向
    if (should_stop_move && !is_stopping_)
    {
        // 满足切换条件，开始停车过程
        std::cout << "[倒车调整] 移动完成: 时间=" << std::setprecision(1) << move_elapsed_time
                  << "s, 距离=" << std::setprecision(2) << move_distance << "m, 开始停车" << std::endl;

        is_stopping_ = true;
        stop_start_time_ = current_time;
    }

    // 如果正在停车过程中
    if (is_stopping_)
    {
        // 停车控制：速度归零，渐进式转向归零
        forward_speed = 0.0;
        retreat_speed = 80.0;

        // 渐进式转向归零，避免突然反向转向
        static double last_left_angle = left_angle;
        static double last_right_angle = right_angle;

        // 逐渐减小转向角度，而不是突然归零
        if (last_left_angle > 0) {
            last_left_angle = std::max(0.0, last_left_angle - 100.0); // 每次减少100
            left_angle = last_left_angle;
            right_angle = 0.0;
        } else if (last_right_angle > 0) {
            last_right_angle = std::max(0.0, last_right_angle - 100.0); // 每次减少100
            left_angle = 0.0;
            right_angle = last_right_angle;
        } else {
            left_angle = 0.0;
            right_angle = 0.0;
        }

        gear = 0x22; // N档，确保安全停车

        double stop_elapsed_time = current_time - stop_start_time_;

        // 检查车辆是否停稳：车轮速度足够小 且 停车时间足够长
        bool is_speed_stable = fabs(wheel_speed) < wheel_speed_threshold_;
        bool is_time_enough = stop_elapsed_time >= 3.0; // 至少停车1秒

        // 简化停车日志：每1秒打印一次
        static double last_stop_log_time = 0.0;
        if (current_time - last_stop_log_time > 1.0) {
            std::cout << "[倒车调整] 停车中: 车速=" << std::setprecision(1) << fabs(wheel_speed)
                      << ", 时间=" << std::setprecision(1) << stop_elapsed_time << "s" << std::endl;
            last_stop_log_time = current_time;
        }

        if (is_speed_stable && is_time_enough)
        {
            // 车辆已停稳，可以安全切换档位
            std::cout << "[倒车调整] 车辆已停稳，切换移动方向" << std::endl;

            // 切换移动方向
            is_moving_forward_ = !is_moving_forward_;

            // 重置移动状态
            move_start_time_ = current_time;
            move_start_x_ = current_x;
            move_start_y_ = current_y;
            is_stopping_ = false;

            // 切换到目标档位
            gear = is_moving_forward_ ? 0x21 : 0x24; // D档或R档

            std::cout << "[倒车调整] 切换到" << (is_moving_forward_ ? "前进" : "后退")
                      << "模式，档位=" << std::hex << (int)gear << std::dec << std::endl;
        }

        return false; // 继续调整
    }

    // 执行移动控制 - 优化的平滑转向控制
    // 计算动态转向角度，减少激进程度
    double dynamic_steering = steering_angle_ * (1.0 + abs_yaw_error / (M_PI / 2.0)); // 降低激进系数
    dynamic_steering = std::min(dynamic_steering, 800.0); // 降低最大转向角度

    // 添加转向平滑处理，避免左右摆头
    static double last_steering = 0.0;
    double steering_change = dynamic_steering - last_steering;
    if (fabs(steering_change) > 200.0) {
        // 限制转向变化幅度，避免突然大幅转向
        dynamic_steering = last_steering + (steering_change > 0 ? 200.0 : -200.0);
    }
    last_steering = dynamic_steering;

    if (is_moving_forward_)
    {
        // 前进并转向
        gear = 0x21; // D档

        // 动态速度调整：启步时使用高速度，车辆开始移动后降低速度
        if (wheel_speed > speed_switch_threshold_) {
            forward_speed = adjustment_speed_; // 车辆已移动，使用正常速度30
        } else {
            forward_speed = forward_start_speed_; // 启步阶段，使用高速度40
        }
        retreat_speed = 0.0;

        // 根据角度误差确定转向方向，添加稳定性检查
        static int left_count = 0, right_count = 0;

        if (yaw_error > 0.1) { // 增加死区，避免小误差时频繁切换
            left_count++;
            right_count = 0;
            if (left_count >= 3) { // 连续3次确认方向后才转向
                left_angle = dynamic_steering;
                right_angle = 0.0;
            }
        }
        else if (yaw_error < -0.1) {
            right_count++;
            left_count = 0;
            if (right_count >= 3) { // 连续3次确认方向后才转向
                left_angle = 0.0;
                right_angle = dynamic_steering;
            }
        }
        else {
            // 误差很小时保持当前转向
            left_count = right_count = 0;
        }
    }
    else
    {
        // 后退并转向
        gear = 0x24; // R档

        // 动态速度调整：启步时使用高速度，车辆开始移动后降低速度
        if (wheel_speed > speed_switch_threshold_) {
            forward_speed = adjustment_speed_ - 10; // 车辆已移动，使用正常速度20 (30-10)
        } else {
            forward_speed = reverse_start_speed_; // 启步阶段，使用高速度40
        }
        retreat_speed = 0.0;

        // 后退时转向方向相反，添加稳定性检查
        static int back_left_count = 0, back_right_count = 0;

        if (yaw_error > 0.1) { // 增加死区，避免小误差时频繁切换
            back_right_count++;
            back_left_count = 0;
            if (back_right_count >= 3) { // 连续3次确认方向后才转向
                left_angle = 0.0;
                right_angle = dynamic_steering;
            }
        }
        else if (yaw_error < -0.1) {
            back_left_count++;
            back_right_count = 0;
            if (back_left_count >= 3) { // 连续3次确认方向后才转向
                left_angle = dynamic_steering;
                right_angle = 0.0;
            }
        }
        else {
            // 误差很小时保持当前转向
            back_left_count = back_right_count = 0;
        }
    }

    // 简化移动日志：每2秒打印一次状态
    static double last_move_log_time = 0.0;
    if (current_time - last_move_log_time > 2.0) {
        double current_speed = is_moving_forward_ ? forward_speed : forward_speed;
        std::string speed_mode = (wheel_speed > speed_switch_threshold_) ? "正常" : "启步";

        std::cout << "[倒车调整] " << (is_moving_forward_ ? "前进" : "后退")
                  << " 角度误差=" << std::setprecision(1) << (abs_yaw_error * 180.0 / M_PI)
                  << "° 时间=" << std::setprecision(1) << move_elapsed_time
                  << "s 距离=" << std::setprecision(1) << move_distance << "m"
                  << " 轮速=" << std::setprecision(2) << wheel_speed
                  << " 速度=" << std::setprecision(0) << current_speed << "(" << speed_mode << ")" << std::endl;
        last_move_log_time = current_time;
    }

    return false; // 调整未完成
}

void ReverseAdjustmentModule::reset()
{
    is_reversing_ = false;
    adjustment_complete_ = false;
    target_yaw_ = 0.0;
    start_time_ = 0.0;

    std::cout << "[倒车调整] 倒车调整模块已重置" << std::endl;
}

double ReverseAdjustmentModule::getYawFromQuaternion(const geometry_msgs::Quaternion &orientation)
{
    tf::Quaternion q(orientation.x, orientation.y, orientation.z, orientation.w);
    tf::Matrix3x3 m(q);
    double roll, pitch, yaw;
    m.getRPY(roll, pitch, yaw);
    return yaw;
}

double ReverseAdjustmentModule::normalizeAngle(double angle)
{
    while (angle > M_PI)
        angle -= 2.0 * M_PI;
    while (angle < -M_PI)
        angle += 2.0 * M_PI;
    return angle;
}
