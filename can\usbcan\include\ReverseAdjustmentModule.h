#ifndef REVERSE_ADJUSTMENT_MODULE_H
#define REVERSE_ADJUSTMENT_MODULE_H

#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <tf/tf.h>
#include <tf/transform_datatypes.h>
#include "djyxlogger.h"

/**
 * @brief 倒车调整模块
 * 
 * 负责处理车辆到达目标点后的90度转向调整功能
 */
class ReverseAdjustmentModule
{
public:
    /**
     * @brief 构造函数
     * @param logger 日志记录器引用
     */
    explicit ReverseAdjustmentModule(djyx::Logger &logger);

    /**
     * @brief 析构函数
     */
    ~ReverseAdjustmentModule() = default;

    /**
     * @brief 开始倒车调整
     * @param current_pose 当前车辆位姿
     * @param goal_x 目标点X坐标
     * @param goal_y 目标点Y坐标
     * @param direction 倒车方向："left"或"right"，默认"left"
     */
    void startReverseAdjustment(const geometry_msgs::Pose &current_pose, double goal_x, double goal_y, const std::string& direction = "left");

    /**
     * @brief 更新倒车调整过程
     * @param current_pose 当前车辆位姿
     * @param wheel_speed 当前车轮速度
     * @param control_mode 车辆控制模式状态
     * @param forward_speed 前进速度输出
     * @param retreat_speed 后退速度输出
     * @param left_angle 左转角度输出
     * @param right_angle 右转角度输出
     * @param gear 档位输出
     * @return true 如果调整完成，false 如果仍在调整中
     */
    bool updateReverseAdjustment(const geometry_msgs::Pose &current_pose,
                                double wheel_speed,
                                bool control_mode,
                                double &forward_speed,
                                double &retreat_speed,
                                double &left_angle,
                                double &right_angle,
                                char &gear);

    /**
     * @brief 检查是否正在进行倒车调整
     * @return true 如果正在倒车调整，false 否则
     */
    bool isReversing() const { return is_reversing_; }

    /**
     * @brief 检查倒车调整是否完成
     * @return true 如果调整完成，false 否则
     */
    bool isAdjustmentComplete() const { return adjustment_complete_; }

    /**
     * @brief 重置倒车调整状态
     */
    void reset();

    /**
     * @brief 设置角度调整精度（度）
     * @param precision 精度值，默认5度
     */
    void setAnglePrecision(double precision) { angle_precision_ = precision * M_PI / 180.0; }

    /**
     * @brief 设置调整速度
     * @param speed 调整时的速度，默认15.0
     */
    void setAdjustmentSpeed(double speed) { adjustment_speed_ = speed; }

    /**
     * @brief 设置转向角度
     * @param angle 转向角度，默认300.0
     */
    void setSteeringAngle(double angle) { steering_angle_ = angle; }

    /**
     * @brief 设置前进后退切换间隔（秒）
     * @param interval 切换间隔，默认2.0秒
     */
    void setSwitchInterval(double interval) { switch_interval_ = interval; }

    /**
     * @brief 设置刹车力度
     * @param force 刹车力度，默认150.0（柔和刹车）
     */
    void setBrakeForce(double force) { brake_force_ = force; }

    /**
     * @brief 设置位置精度
     * @param precision 位置精度（米），默认0.5米
     */
    void setPositionPrecision(double precision) { position_precision_ = precision; }

    /**
     * @brief 设置单次移动最大距离
     * @param distance 最大距离（米），默认1.0米
     */
    void setMaxMoveDistance(double distance) { max_move_distance_ = distance; }

    /**
     * @brief 设置车轮速度阈值
     * @param threshold 速度阈值，默认0.1（判断车辆是否停稳）
     */
    void setWheelSpeedThreshold(double threshold) { wheel_speed_threshold_ = threshold; }

private:
    /**
     * @brief 从四元数获取yaw角度
     * @param orientation 四元数
     * @return yaw角度（弧度）
     */
    double getYawFromQuaternion(const geometry_msgs::Quaternion &orientation);

    /**
     * @brief 角度归一化到[-π, π]
     * @param angle 输入角度
     * @return 归一化后的角度
     */
    double normalizeAngle(double angle);

    // 日志记录器
    djyx::Logger &logger_;

    // 状态变量
    bool is_reversing_;           // 是否正在倒车调整
    bool adjustment_complete_;    // 调整是否完成
    double target_yaw_;          // 目标yaw角度
    double start_time_;          // 开始调整的时间
    std::string reverse_direction_; // 倒车方向："left"或"right"

    // 配置参数
    double angle_precision_;     // 角度精度（弧度）
    double position_precision_;  // 位置精度（米）
    double adjustment_speed_;    // 调整速度
    double forward_start_speed_; // 前进启步速度
    double reverse_start_speed_; // 倒车启步速度
    double speed_switch_threshold_; // 速度切换阈值
    double steering_angle_;      // 转向角度
    double switch_interval_;     // 前进后退切换间隔（秒）
    double brake_force_;         // 刹车力度
    double max_move_distance_;   // 单次移动最大距离（米）

    // 状态跟踪
    double start_x_, start_y_;   // 开始调整时的位置
    double last_move_distance_; // 上次移动的距离
    bool is_moving_forward_;    // 当前是否在前进
    bool last_control_mode_;    // 上次的控制模式状态
    double move_start_time_;    // 当前移动开始时间
    double move_start_x_, move_start_y_; // 当前移动开始位置

    // 停车和档位切换状态
    bool is_stopping_;          // 是否正在停车
    double stop_start_time_;    // 停车开始时间
    double wheel_speed_threshold_; // 车轮速度阈值，判断是否停稳
};

#endif // REVERSE_ADJUSTMENT_MODULE_H
