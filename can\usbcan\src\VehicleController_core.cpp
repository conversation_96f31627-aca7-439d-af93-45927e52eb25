#include "VehicleController.h"
#include "VelocityToPulse.h"
#include <filesystem>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <iostream>
#include <yaml-cpp/yaml.h>

void VehicleController::HandleCanObject()
{
    static auto last_can_message_time = std::chrono::steady_clock::now();
    static int no_message_count = 0;

    try {
        // 接收CAN对象
        std::optional<VCI_CAN_OBJ> can_obj_opt = usbCan.receive(0);

        if (can_obj_opt)
        {
            // 提取CAN对象
            const VCI_CAN_OBJ &can_obj = *can_obj_opt;

            // 更新最后接收消息时间
            last_can_message_time = std::chrono::steady_clock::now();
            no_message_count = 0;

        // 更新CAN数据映射，记录接收时间戳
        updateCanData(can_obj);

        // 根据CAN消息ID处理不同的消息类型
        switch (can_obj.ID)
        {
        case 0x110:
            // 处理车身故障消息
            // HandleBodyFaultMessage(can_obj);
            break;
        case 0x301:
            // 处理按钮消息
            HandleButtonMessage(can_obj);
            break;
        case 0x302:
            // 处理另一个按钮消息
            HandleButtonMessage2(can_obj);
            break;
        case 0x283:
            // 处理车辆控制模式消息
            HandleVehicleControlModeMessage(can_obj);
            break;
        case 0x288:
            // 处理电池管理系统（BMS）消息
            HandleBmsMessage(can_obj);
            break;
        case 0x287:
            // 处理速度消息
            HandleSpeedMessage(can_obj);
            break;
        default:
            // 处理未知或未实现的消息ID
            // std::cout << "Received unknown CAN message with ID: " << std::hex << can_obj.ID << std::endl;
            break;
        }
    }
    else
        {
            // 没有接收到CAN消息，检查超时
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_can_message_time);

            no_message_count++;

            // 如果超过5秒没有收到CAN消息，报告问题
            if (elapsed.count() >= 5 && no_message_count % 1000 == 0) {
                printf("\n⚠️  CAN TIMEOUT! No messages for %ld seconds (count: %d)\n", elapsed.count(), no_message_count);
                fflush(stdout);
                ROS_WARN("CAN communication timeout! No messages received for %ld seconds", elapsed.count());
            }
        }
    }
    catch (const std::exception& e) {
        printf("\n❌ CAN EXCEPTION: %s\n", e.what());
        fflush(stdout);
        ROS_ERROR("Exception in HandleCanObject: %s", e.what());
    }
    catch (...) {
        printf("\n❌ CAN UNKNOWN EXCEPTION\n");
        fflush(stdout);
        ROS_ERROR("Unknown exception in HandleCanObject");
    }
}

// 车身故障
void VehicleController::HandleBodyFaultMessage(const VCI_CAN_OBJ &can_obj)
{
    for (int i = 0; i < can_obj.DataLen; ++i)
    {
        if (static_cast<int>(can_obj.Data[i]) != 0)
        {
            switch (i)
            {
            case 0:
                logger_.logError("Emergency stop!");
                PublishErrorInfo(1, 0);
                break;
            case 1:
                logger_.logError("Collision strip activation!");
                PublishErrorInfo(1, 1);
                break;
            case 2:
                logger_.logError("Ultrasonic activation!");
                PublishErrorInfo(1, 2);
                break;
            case 3:
                logger_.logError("Remote control lost!");
                PublishErrorInfo(1, 3);
                break;
            case 5:
            case 6:
            case 7:
                logger_.logError("Brake fault, Error code: " + std::to_string(static_cast<int>(can_obj.Data[i])));
                switch (i)
                {
                case 5:
                    PublishErrorInfo(static_cast<int>(can_obj.Data[i]), 4);
                    break;
                case 6:
                    PublishErrorInfo(static_cast<int>(can_obj.Data[i]), 5);
                    break;
                case 7:
                    PublishErrorInfo(static_cast<int>(can_obj.Data[i]), 6);
                    break;
                }
                break;
            }
            if (static_cast<int>(can_obj.Data[i]) == 0)
            {
                switch (i)
                {
                case 4:
                    logger_.logError("Zero oil pressure-Hydraulic fault!");
                    break;
                default:
                    break;
                }
            }
        }
    }
}

// 按钮
void VehicleController::HandleButtonMessage(const VCI_CAN_OBJ &can_obj)
{
    // 解析按钮消息数据
    can_msg::button Button_data(can_obj.Data);

    int button = Button_data.button_data();

    // 添加详细调试信息 - 使用慢速输出
    static int debug_counter = 0;
    static auto last_debug_time = std::chrono::steady_clock::now();
    debug_counter++;

    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_debug_time);

    // 每3秒或者有重要事件时才输出
    bool should_print = (elapsed.count() >= 3) || (button_pre != button && button != 0 && button < 17);

    if (should_print) {
        // printf("\n[%d] 🔘 BUTTON=%d (prev=%d) ", debug_counter, button, button_pre);
        fflush(stdout);
        last_debug_time = current_time;

        // ROS_WARN("=== BUTTON DEBUG === received button=%d, button_pre=%d", button, button_pre);
    }

    // 根据按钮数据更新目标位置和计划
    if (button_pre != button && button != 0 && button < 17)
    {
        button_pre = button;
        int i = (BitPosition(button));
        if (should_print) {
            ROS_WARN("=== BUTTON DEBUG === BitPosition(%d) = %d", button, i);
        }

        if (is_BatteryLow)
        {
            ROS_WARN("[DEBUG] Battery low, forcing button to 2");
            i = 2;
        }
        // 恢复原始映射：直接使用BitPosition结果，不需要+1
        std::string button_name = "button" + std::to_string(i);
        if (should_print) {
            ROS_WARN("=== BUTTON DEBUG === Looking for goal: %s", button_name.c_str());
        }

        auto it = goal_map.find(button_name);
        if (it != goal_map.end())
        {
            const Goal &goal_data = it->second;
            goal.pose.position.x = goal_data.position_x;
            goal.pose.position.y = goal_data.position_y;
            goal.pose.orientation.z = goal_data.orientation_z;
            goal.pose.orientation.w = goal_data.orientation_w;

            // 明显的成功提示 - 重要事件立即显示
            printf("\n🎯 SUCCESS! Publishing goal (%.2f, %.2f) to %d subscribers\n",
                   goal.pose.position.x, goal.pose.position.y, goal_pub.getNumSubscribers());
            fflush(stdout);

            // 详细日志使用慢速输出
            if (should_print) {
                ROS_ERROR("=== GOAL PUBLISHED === x=%.2f, y=%.2f, z=%.2f, w=%.2f",
                         goal.pose.position.x, goal.pose.position.y,
                         goal.pose.orientation.z, goal.pose.orientation.w);

                // 检查发布器状态
                ROS_ERROR("=== GOAL PUBLISHED === subscribers: %d", goal_pub.getNumSubscribers());

                // 验证发布后的状态
                ROS_ERROR("=== GOAL PUBLISHED === SUCCESS to /move_base_simple/goal");
            }

            goal_pub.publish(goal);

            // 简洁的按钮和目标点信息
            std::cout << "[按钮] 按钮" << i << " → 目标点("
                      << std::fixed << std::setprecision(1) << goal_data.position_x
                      << ", " << goal_data.position_y << ")" << std::endl;
            if (goal_data.has_associate)
            {
                have_associate = true;
                associate_goal.pose.position.x = goal_data.associate_goal.position_x;
                associate_goal.pose.position.y = goal_data.associate_goal.position_y;
                Auto_Hook = true;
            }
            else
            {
                have_associate = false;
                Auto_Hook = goal_data.decoupling;
            }
            current_goal_reverse_flag = goal_data.need_reverse; // 设置当前目标点的倒车标志
            current_goal_reverse_direction = goal_data.reverse_direction; // 设置当前目标点的倒车方向
            Arrived = false;
            Mission_Stop = false;
            reverse_module_.reset(); // 重置倒车调整模块

            // 发布倒车状态为false
            std_msgs::Bool reverse_status_msg;
            reverse_status_msg.data = false;
            reverse_status_pub.publish(reverse_status_msg);

            // 重置到达状态标志，以便下次到达时能正确打印
            static bool last_arrived = false;
            last_arrived = false;
        }
        else
        {
            // 错误信息立即显示
            printf("\n❌ GOAL NOT FOUND! Looking for '%s'\n", button_name.c_str());
            printf("Available goals: ");
            for (const auto& pair : goal_map) {
                printf("%s ", pair.first.c_str());
            }
            printf("\n");
            fflush(stdout);

            // 详细日志使用慢速输出
            if (should_print) {
                ROS_ERROR("=== GOAL NOT FOUND === %s not found in goal_map", button_name.c_str());
                ROS_ERROR("=== AVAILABLE GOALS ===");
                for (const auto& pair : goal_map) {
                    ROS_ERROR("  - %s", pair.first.c_str());
                }
            }
        }
    }
    else
    {
        // 忽略信息使用慢速输出，避免刷屏
        static auto last_ignore_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        auto ignore_elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_ignore_time);

        if (ignore_elapsed.count() >= 3) {
            // printf("❌ IGNORED! ");
            if (button_pre == button) {
                // printf("(same as previous) ");
            }
            // if (button == 0) {
            //     printf("(button=0) ");
            // }
            // if (button >= 17) {
            //     printf("(out of range) ");
            // }
            // printf("\n");
            fflush(stdout);
            last_ignore_time = current_time;

            // ROS_WARN("=== BUTTON IGNORED === button_pre=%d, button=%d, button<17=%s",
                    //  button_pre, button, (button < 17) ? "true" : "false");
            // if (button_pre == button) {
            //     ROS_WARN("=== BUTTON IGNORED === Same button as previous");
            // }
            // if (button == 0) {
            //     ROS_WARN("=== BUTTON IGNORED === Button is 0");
            // }
            // if (button >= 17) {
            //     ROS_WARN("=== BUTTON IGNORED === Button >= 17 (out of range)");
            // }
        }
    }
}

void VehicleController::HandleButtonMessage2(const VCI_CAN_OBJ &can_obj)
{
    // 解析按钮消息数据
    can_msg::button Button_data(can_obj.Data);
    int button = Button_data.button_data();

    ROS_WARN("=== BUTTON2 DEBUG === received button=%d, button_pre=%d", button, button_pre);

    // 根据按钮数据更新目标位置和计划
    if (button == 4)
    {
        ROS_INFO("[DEBUG] Voice button pressed, toggling Voice_flag");
        Voice_flag = !Voice_flag;
    }
    if (button_pre != button && button != 0 && button < 3)
    {
        button_pre = button;
        int i = (BitPosition(button));
        ROS_INFO("[DEBUG] BitPosition(%d) = %d", button, i);

        if (is_BatteryLow)
        {
            ROS_WARN("[DEBUG] Battery low, forcing button to 2");
            i = 2;
        }
        // 恢复原始映射：直接使用BitPosition结果，不需要+1
        std::string button_name = "button" + std::to_string(i);
        ROS_INFO("button_recieve:%d", button);
        ROS_INFO("button:%d", i);
        ROS_INFO("[DEBUG] Looking for goal: %s", button_name.c_str());

        auto it = goal_map.find(button_name);
        if (it != goal_map.end())
        {
            const Goal &goal_data = it->second;
            goal.pose.position.x = goal_data.position_x;
            goal.pose.position.y = goal_data.position_y;
            goal.pose.orientation.z = goal_data.orientation_z;
            goal.pose.orientation.w = goal_data.orientation_w;

            ROS_INFO("[DEBUG] Publishing goal: x=%.2f, y=%.2f, z=%.2f, w=%.2f",
                     goal.pose.position.x, goal.pose.position.y,
                     goal.pose.orientation.z, goal.pose.orientation.w);

            // 检查发布器状态
            ROS_INFO("[DEBUG] goal_pub subscribers: %d", goal_pub.getNumSubscribers());

            goal_pub.publish(goal);

            // 验证发布后的状态
            ROS_INFO("[DEBUG] Goal published successfully to /move_base_simple/goal");
            if (goal_data.has_associate)
            {
                have_associate = true;
                associate_goal.pose.position.x = goal_data.associate_goal.position_x;
                associate_goal.pose.position.y = goal_data.associate_goal.position_y;
                Auto_Hook = true;
            }
            else
            {
                have_associate = false;
                Auto_Hook = goal_data.decoupling;
            }
            current_goal_reverse_flag = goal_data.need_reverse; // 设置当前目标点的倒车标志
            current_goal_reverse_direction = goal_data.reverse_direction; // 设置当前目标点的倒车方向
            Arrived = false;
            Mission_Stop = false;
            reverse_module_.reset(); // 重置倒车调整模块

            // 发布倒车状态为false
            std_msgs::Bool reverse_status_msg;
            reverse_status_msg.data = false;
            reverse_status_pub.publish(reverse_status_msg);
        }
        else
        {
            ROS_WARN("[DEBUG] Goal with name %s not found in goal_map", button_name.c_str());
            ROS_WARN("[DEBUG] Available goals in map:");
            for (const auto& pair : goal_map) {
                ROS_WARN("[DEBUG]   - %s", pair.first.c_str());
            }
        }
    }
    else
    {
        ROS_INFO("[DEBUG] Button2 condition not met: button_pre=%d, button=%d, button<3=%s",
                 button_pre, button, (button < 3) ? "true" : "false");
        if (button_pre == button) {
            ROS_INFO("[DEBUG] Same button as previous (button_pre == button)");
        }
        if (button == 0) {
            ROS_INFO("[DEBUG] Button is 0 (ignored)");
        }
        if (button >= 3) {
            ROS_INFO("[DEBUG] Button >= 3 (out of range for HandleButtonMessage2)");
        }
    }
}

// 检测车辆自动驾驶模式
void VehicleController::HandleVehicleControlModeMessage(const VCI_CAN_OBJ &can_obj)
{
    // 解析车辆控制模式消息
    can_msg::vechicle_control data(can_obj.Data);
    ControlMode = data.control_mode();
}

// 电池的电量状态
void VehicleController::HandleBmsMessage(const VCI_CAN_OBJ &can_obj)
{
    // 解析电池管理系统（BMS）消息
    can_msg::DJ2_BMS bms(can_obj.Data);
    soc.data = bms.YCB_SOC();
    is_BatteryLow = soc.data <= 10 ? true : false;
    if (is_BatteryLow)
    {
        logger_.logError("Battery Low!");
    }
    soc_pub.publish(soc);
}

// 解析车速数据
void VehicleController::HandleSpeedMessage(const VCI_CAN_OBJ &can_obj)
{
    // 处理速度消息，更新轮速数据并发布
    int seq = 0;
    VelocityToPulse rl_v2p(0.72f, 4096);
    VelocityToPulse rr_v2p(0.72f, 4096);
    can_msg::DJ2_speed velo(can_obj.Data);
    auto wheels = boost::make_shared<usbcan_msgs::WheelsEncoder>();
    wheels->header.seq = seq++;
    wheels->header.stamp = ros::Time::now();
    wheels->header.frame_id = "";
    double ratio = 12.7;
    int wheel_dimameter = 500;
    wheel_speed = velo.speed() / 60 * 3.1415926 / ratio * wheel_dimameter / 1000 * 3.6;
    wheels->left_encoder = rl_v2p(wheel_speed);
    wheels->right_encoder = rr_v2p(wheel_speed);
    wheels->right_speed = wheel_speed;
    wheels->left_speed = wheel_speed;
    wheels_pub.publish(wheels);
    std_msgs::Float32 sp;
    sp.data = wheel_speed;
    speed_pub.publish(sp);
}

void VehicleController::ProcessLoop()
{
    // 主处理循环，设置合适的频率避免CPU过度占用
    ros::Rate loop_rate(1000); // 1000Hz，确保CAN消息及时处理

    printf("\n🚀 ProcessLoop started\n");
    fflush(stdout);
    ROS_INFO("VehicleController ProcessLoop started");

    try {
        while (ros::ok())
        {
            // 处理CAN对象
            HandleCanObject();

            // 检查CAN设备状态
            checkCanDeviceStatus();

            if (!ros::ok())
                break;

            // 适当延时，避免CPU过度占用
            loop_rate.sleep();
        }
    }
    catch (const std::exception& e) {
        printf("\n❌ ProcessLoop EXCEPTION: %s\n", e.what());
        fflush(stdout);
        ROS_ERROR("Exception in ProcessLoop: %s", e.what());
    }
    catch (...) {
        printf("\n❌ ProcessLoop UNKNOWN EXCEPTION\n");
        fflush(stdout);
        ROS_ERROR("Unknown exception in ProcessLoop");
    }

    printf("\n🛑 ProcessLoop ended\n");
    fflush(stdout);
    ROS_WARN("VehicleController ProcessLoop ended");
}

void VehicleController::fakeCanMsgWheels()
{
    // 模拟CAN消息发送循环
    ros::Rate loop_rate(50);

    printf("\n🚀 fakeCanMsgWheels started\n");
    fflush(stdout);
    ROS_INFO("VehicleController fakeCanMsgWheels started");

    try {
        while (ros::ok())
        {
        // 灯光和挂钩，语音提示，按钮消息，档位信息的CAN消息，控制自动驾驶的CAN消息，。
        VCI_CAN_OBJ lightHookCmd{}, voiceCmd{}, buttonCmd{}, driveCmdGear{}, autoDriveCmd{};

        // 控制自动驾驶
        can_msg::DJ2_control driveCmd;
        driveCmd.speed_value(forward_speed);
        driveCmd.break_value(retreat_speed);
        driveCmd.left_value(left_angle);
        driveCmd.right_value(right_angle);

        // 构建控制自动驾驶的CAN消息
        autoDriveCmd.ID = 0x285;
        autoDriveCmd.DataLen = driveCmd.data().size();
        autoDriveCmd.SendType = 0;
        autoDriveCmd.RemoteFlag = 0;
        autoDriveCmd.ExternFlag = 0;
        std::copy(driveCmd.data().begin(), driveCmd.data().end(), autoDriveCmd.Data);

        // 构建档位信息的CAN消息
        driveCmdGear.ID = 0x286;
        driveCmdGear.DataLen = 8;
        driveCmdGear.SendType = 0;
        driveCmdGear.RemoteFlag = 0;
        driveCmdGear.ExternFlag = 0;
        std::fill_n(driveCmdGear.Data, 8, 0);
        driveCmdGear.Data[1] = Gear; // 0x21:D档 0x24:R档 0x22:N档

        // 控制车灯 8:黄灯，16:绿灯
        can_msg::DJ2_light light_Hook;
        if (red_light_flag)
        {
            PublishErrorInfo(1, 7);
            light_Hook.Green_light(0);
            light_Hook.Yellow_light(0);
            light_Hook.Red_light(1);
            light_Hook.Buzzer(1);
        }
        else if (isblock || located_flag || isTooFar || Safety || obstacle_detected)
        {
            light_Hook.Red_light(0);
            light_Hook.Buzzer(0);
            light_Hook.Yellow_light(1);
            light_Hook.Green_light(0);
        }
        else
        {
            light_Hook.Green_light(1);
            light_Hook.Yellow_light(0);
            light_Hook.Red_light(0);
            light_Hook.Buzzer(0);
        }

        // 自动脱钩逻辑
        if (ControlMode && Auto_Hook && (Arrived || associate_arrived))
        {
            if (wheel_speed <= 0.05)
            {
                Hooking = true;
                light_Hook.Hook_up(1);
                light_Hook.Hook_down(0);

                if (Hookup_flag)
                {
                    light_Hook.Hook_up(0);
                    light_Hook.Hook_down(0);
                    Hooking = false;
                    time_count_Hook = false;
                    Auto_Hook = false;
                }
            }
        }

        // 语音提示
        can_msg::DJ2_voice Voice;
        if (ControlMode && Voice_flag)
        {
            if (isblock)
            {
                Voice.brake_voice(1);
            }
            else if (Gear == 0x24)
            {
                Voice.reverse_voice(1);
            }
            else if (left_angle / 1022.0 * 31.0 > 5)
            {
                Voice.turn_left_voice(1);
            }
            else if (right_angle / 1022.0 * 31.0 > 5)
            {
                Voice.turn_right_voice(1);
            }
        }
        else
        {
            Voice.brake_voice(0);
            Voice.reverse_voice(0);
            Voice.turn_left_voice(0);
            Voice.turn_right_voice(0);
        }

        // 构建车灯控制的CAN消息
        lightHookCmd.ID = 0x101;
        lightHookCmd.DataLen = light_Hook.data().size();
        lightHookCmd.SendType = 0;
        lightHookCmd.RemoteFlag = 0;
        lightHookCmd.ExternFlag = 0;
        std::copy(light_Hook.data().begin(), light_Hook.data().end(), lightHookCmd.Data);

        voiceCmd.ID = 0x102;
        voiceCmd.DataLen = Voice.data().size();
        voiceCmd.SendType = 0;
        voiceCmd.RemoteFlag = 0;
        voiceCmd.ExternFlag = 0;
        std::copy(Voice.data().begin(), Voice.data().end(), voiceCmd.Data);

        // 构建按钮继电器主机发送的CAN消息
        buttonCmd.ID = 0X302;
        buttonCmd.SendType = 1;
        buttonCmd.RemoteFlag = 0;
        buttonCmd.ExternFlag = 0;
        buttonCmd.DataLen = 8;
        buttonCmd.Data[0] = 0;

        // // 构建超声波的CAN消息
        // VCI_CAN_OBJ Ultrasonic_send_obj{};
        // Ultrasonic_send_obj.ID = 0x1d2;
        // Ultrasonic_send_obj.SendType = 1;
        // Ultrasonic_send_obj.RemoteFlag = 0;
        // Ultrasonic_send_obj.ExternFlag = 0;
        // Ultrasonic_send_obj.DataLen = 8;
        // Ultrasonic_send_obj.Data[0] = 25;
        // Ultrasonic_send_obj.Data[1] = 30;
        // Ultrasonic_send_obj.Data[2] = 30;
        // Ultrasonic_send_obj.Data[3] = 25;

        // 发送CAN消息
        usbCan.send(autoDriveCmd, 0);
        usbCan.send(driveCmdGear, 0);
        usbCan.send(lightHookCmd, 0);
        usbCan.send(voiceCmd, 0);
        usbCan.send(buttonCmd, 0);

        // if (Ultrasonic_send_flag < 100)
        // {
        //     usbCan.send(Ultrasonic_send_obj, 0);
        //     Ultrasonic_send_flag++;
        //     // ROS_INFO("Ultrasonic_send_flag: %d", Ultrasonic_send_flag);
        // }

        loop_rate.sleep();
        }
    }
    catch (const std::exception& e) {
        printf("\n❌ fakeCanMsgWheels EXCEPTION: %s\n", e.what());
        fflush(stdout);
        ROS_ERROR("Exception in fakeCanMsgWheels: %s", e.what());
    }
    catch (...) {
        printf("\n❌ fakeCanMsgWheels UNKNOWN EXCEPTION\n");
        fflush(stdout);
        ROS_ERROR("Unknown exception in fakeCanMsgWheels");
    }

    printf("\n🛑 fakeCanMsgWheels ended\n");
    fflush(stdout);
    ROS_WARN("VehicleController fakeCanMsgWheels ended");
}

void VehicleController::ctrlCmdCallback(const auto_msgs::ControlCommandStampedConstPtr &msg)
{
    // 如果正在进行倒车调整，不处理正常的控制命令
    if (reverse_module_.isReversing())
    {
        // std::cout << "[车辆控制] 倒车调整中，忽略正常控制命令" << std::endl;
        return;
    }

    static float last_velocity = 0;  // 保存上一次的速度值
    static float integral = 0;       // 积分项
    static float previous_error = 0; // 上一次的误差

    // PID控制器参数
    float Kp = 10.0; // 比例系数
    float Ki = 0.1;  // 积分系数
    float Kd = 0.01; // 微分系数

    // 计算方向盘角度
    double angle = std::clamp(msg->cmd.steering_angle *
                                  (180.0 / M_PI) / 31.0 * 1022.0,
                              -1200.0, 1200.0);

    // 设置左右转向角
    left_angle = angle >= 0 ? angle : 0;
    right_angle = angle < 0 ? -angle : 0;

    float velocity = (msg->cmd.linear_velocity) * 36; // 得到车身线性速度(Km/h)

    /**
     * @brief 刹车逻辑
     *
     * @param isblock 路径是否被挡
     * @param located_flag 定位是否正常
     * @param Hooking 自动脱钩是否正在运行
     * @param is_stopped 是否已经停车(使用在自动脱钩后继续出发功能)
     * @param Mission_Stop 任务暂停停车
     * @param Ultrasonic_stop 超声波检测停车
     * @param isTooFar 车身位姿是否偏移和离路径是否过远
     * @param topic_stop 指定话题是否在持续更新
     */
    if (velocity * last_velocity < 0 || topic_stop || velocity == 0 || isblock ||
        located_flag || Hooking || is_stopped || Mission_Stop || isTooFar || Safety || obstacle_detected)
    {
        // 简洁的停车原因日志，每5秒打印一次
        static double last_stop_log_time = 0.0;
        double current_time = ros::Time::now().toSec();
        if (current_time - last_stop_log_time > 5.0)
        {
            std::string stop_reason = "";
            if (obstacle_detected)
                stop_reason = "a*道路阻塞";
            else if (isblock)
                stop_reason = "op道路阻塞";
            else if (located_flag)
                stop_reason = "定位丢失";
            else if (Mission_Stop)
                stop_reason = "任务暂停";
            else if (isTooFar)
                stop_reason = "偏离路径";
            else if (topic_stop)
                stop_reason = "话题停止";
            else if (Hooking)
                stop_reason = "挂钩中";
            else if (is_stopped)
                stop_reason = "已停车";
            else if (velocity * last_velocity < 0)
                stop_reason = "速度反向";
            else if (Safety)
                stop_reason = "安全框触发";

            if (!stop_reason.empty())
            {
                std::cout << "[停车] 原因: " << stop_reason << std::endl;
                last_stop_log_time = current_time;
            }
        }

        // 保留原有的详细错误日志（但频率较低）
        if (isblock)
        {
            logger_.logError("lane is block !");
        }
        else if (located_flag)
        {
            logger_.logError("location lost !");
        }
        else if (Mission_Stop)
        {
            logger_.logError("mission stop !");
        }
        else if (isTooFar)
        {
            logger_.logError("Far away from lane !");
        }

        forward_speed = 0;

        if (wheel_speed == 0)
        {
            retreat_speed = 0;
        }
        else
        {
            // PID控制计算retreat_speed
            double error = 0 - wheel_speed;                                     // 期望速度为0，误差为负的wheel_speed
            integral += error;                                                  // 积分项累加误差
            double derivative = error - previous_error;                         // 计算误差变化率
            retreat_speed = 250 + Kp * error + Ki * integral + Kd * derivative; // PID公式计算retreat_speed

            // 限制retreat_speed的范围
            retreat_speed = std::clamp(retreat_speed, 250.0, 1000.0);

            previous_error = error; // 保存当前误差作为下次的previous_error
        }
    }
    else if (velocity > 0)
    {
        Gear = 0x21;
        error_elimination = true;
        forward_speed = velocity; // 前进速度
        retreat_speed = 0;        // 当不在刹车逻辑中时重置为初始值
        integral = 0;             // 重置积分项
        previous_error = 0;       // 重置误差
        // std::cout << "velocity: " << velocity << std::endl;
    }
    else if (velocity < 0)
    {
        Gear = 0x24;
        forward_speed = -velocity; // 后退速度
        retreat_speed = 0;
    }

    last_velocity = velocity; // 保存当前速度作为下一次的last_velocity
}

void VehicleController::PoseCallback(const geometry_msgs::PoseStampedConstPtr &msg)
{
    double pose_x = msg->pose.position.x;
    double pose_y = msg->pose.position.y;
    double distance_goal = calculateDistance(goal.pose.position, msg->pose.position);
    double distance_2associate_goal;

    if (have_associate)
    {
        // 计算距离
        distance_2associate_goal = calculateDistance(associate_goal.pose.position, msg->pose.position);

        if (!is_stopped && distance_2associate_goal < 1.5 && !has_stopped)
        {
            // 停车并记录停车开始的时间
            is_stopped = true;
            stop_time_start_ = ros::Time::now();
            has_stopped = true;
            associate_arrived = true;
        }
        else if ((ros::Time::now() - stop_time_start_).toSec() > 5.0 && is_stopped)
        {
            // 如果已经停车5秒，恢复行驶
            is_stopped = false;
            if (has_stopped)
            {
                associate_arrived = false;
                have_associate = false;
            }
        }
    }
    else
    {
        // 如果不满足have_associate条件，重置停车状态并计算与目标的距离
        // 如果没有停车且距离目标小于5
        if (distance_goal < 5)
        {
            forward_speed = std::max(forward_speed - 10, 0.0); // 减速，防止负速度

            // 到达目标点时的简洁日志（只在状态变化时打印）
            static bool last_arrived = false;
            if (!last_arrived)
            {
                std::cout << "[到达] 目标点(" << std::fixed << std::setprecision(1)
                          << goal.pose.position.x << ", " << goal.pose.position.y
                          << ") 距离=" << std::setprecision(1) << distance_goal << "m" << std::endl;
                last_arrived = true;
            }

            Arrived = true;
            has_stopped = false;

            // 检查当前目标点是否需要倒车调整
            if (current_goal_reverse_flag && wheel_speed <= 0.05 && !reverse_module_.isReversing() && !reverse_module_.isAdjustmentComplete())
            {
                // 开始倒车调整过程
                double goal_x = goal.pose.position.x;
                double goal_y = goal.pose.position.y;
                reverse_module_.startReverseAdjustment(msg->pose, goal_x, goal_y, current_goal_reverse_direction);

                // 发布倒车状态
                std_msgs::Bool reverse_status_msg;
                reverse_status_msg.data = true;
                reverse_status_pub.publish(reverse_status_msg);
            }
        }

        // 倒车调整逻辑
        if (reverse_module_.isReversing())
        {
            // 在倒车调整过程中检查障碍物
            if (Safety || obstacle_detected)
            {
                // 检测到障碍物，停止倒车调整
                forward_speed = 0.0;
                retreat_speed = 100.0;
                left_angle = 0.0;
                right_angle = 0.0;
                Gear = 0x22; // N档

                // 每5秒打印一次障碍物警告
                static double last_reverse_obstacle_log = 0.0;
                double current_time = ros::Time::now().toSec();
                if (current_time - last_reverse_obstacle_log > 5.0) {
                    std::cout << "[倒车调整] 检测到障碍物，暂停倒车调整" << std::endl;
                    last_reverse_obstacle_log = current_time;
                }
            }
            else
            {
                // 没有障碍物，继续倒车调整
                bool adjustment_completed = reverse_module_.updateReverseAdjustment(msg->pose, wheel_speed, ControlMode,
                                                        forward_speed, retreat_speed,
                                                        left_angle, right_angle, Gear);

                // 如果倒车调整完成，发布状态
                if (adjustment_completed)
                {
                    std_msgs::Bool reverse_status_msg;
                    reverse_status_msg.data = false;
                    reverse_status_pub.publish(reverse_status_msg);
                }
            }
        }
    }
}

void VehicleController::Event_Timer(const ros::TimerEvent &event)
{
    // 获取当前时间一次，以便在整个函数中使用，确保时间的一致性
    ros::Time now = ros::Time::now();

    // 处理红灯警告计时器
    handleRedLightTimer(now);

    // 处理自动脱钩计时器
    handleHookupTimer(now);
}

void VehicleController::handleRedLightTimer(const ros::Time &now)
{
    // 判断是否需要开始计时
    if ((isblock || located_flag || isTooFar || topic_stop) && !time_count && ControlMode && !Arrived)
    {
        start_time = now;  // 记录开始计时的时间
        time_count = true; // 标记已开始计时
    }
    else if (wheel_speed > SPEED_THRESHOLD || !ControlMode)
    {
        // 如果车速大于阈值或不在控制模式，停止计时
        time_count = false;
    }

    // 处理红灯警告标志
    if (time_count && start_time.isValid() && now >= start_time)
    {
        double elapsed = (now - start_time).toSec();
        // 检查时间是否在合理范围内，防止系统时间异常
        if (elapsed > RED_LIGHT_TIMEOUT && elapsed < MAX_TIMER_DURATION)
        {
            red_light_flag = true;
        }
        else if (elapsed <= RED_LIGHT_TIMEOUT)
        {
            red_light_flag = false;
        }
    }
    else
    {
        // 如果没有在计时或时间无效，确保红灯警告标志被重置
        red_light_flag = false;
    }
}

void VehicleController::handleHookupTimer(const ros::Time &now)
{
    // 判断是否需要开始自动脱钩计时
    if (ControlMode && wheel_speed <= SPEED_THRESHOLD && Auto_Hook && !time_count_Hook)
    {
        start_time_Hook = now;  // 记录开始脱钩计时的时间
        time_count_Hook = true; // 标记已开始脱钩计时
    }
    else if (wheel_speed > SPEED_THRESHOLD || !Auto_Hook)
    {
        time_count_Hook = false;
    }

    // 处理脱钩标志
    if (time_count_Hook && start_time_Hook.isValid() && now >= start_time_Hook)
    {
        double elapsed = (now - start_time_Hook).toSec();
        // 检查时间是否在合理范围内
        if (elapsed > HOOKUP_TIMEOUT && elapsed < MAX_TIMER_DURATION)
        {
            Hookup_flag = true;
        }
        else if (elapsed <= HOOKUP_TIMEOUT)
        {
            Hookup_flag = false;
        }
    }
    else
    {
        // 如果没有在计时或时间无效，确保脱钩标志被重置
        Hookup_flag = false;
    }
}

void VehicleController::TopicStateCallback(const auto_msgs::TopicState::ConstPtr &msg)
{
    last_received_ = ros::Time::now();

    topic_flag = msg->topic_state;
    topic = msg->topic;
    if (!topic_flag && accept_messages_)
    {
        logger_.logError(topic + " Error!");
        topic_stop = true;
        accept_messages_ = false; // 设置为不再接受消息
        block_topic_ = topic;     // 记录被阻塞的话题名称
    }
    else if (topic == block_topic_)
    {
        if (topic_flag)
        {
            accept_messages_ = true;
            topic_stop = false;
            block_topic_ = " ";
        }
    }
}

// CAN数据记录和清理功能实现
void VehicleController::loadCanLoggingConfig()
{
    try
    {
        // 获取包路径
        std::string package_path = ros::package::getPath("usbcan");
        if (package_path.empty())
        {
            ROS_WARN("Failed to get package path for usbcan, using default config");
            // 使用默认配置
            can_log_interval_sec_ = 60;
            can_log_retention_days_ = 7;
            enable_logging_ = true;
            enable_real_time_flush_ = true;
            max_log_file_size_mb_ = 100;
            log_file_prefix_ = "can_data";
            return;
        }

        std::string config_file = package_path + "/config/can_logging.yaml";

        // 检查配置文件是否存在
        if (!std::filesystem::exists(config_file))
        {
            ROS_WARN("CAN logging config file not found: %s, using default config", config_file.c_str());
            // 使用默认配置
            can_log_interval_sec_ = 60;
            can_log_retention_days_ = 7;
            enable_logging_ = true;
            enable_real_time_flush_ = true;
            max_log_file_size_mb_ = 100;
            log_file_prefix_ = "can_data";
            return;
        }

        // 加载YAML配置文件
        YAML::Node config = YAML::LoadFile(config_file);

        // 读取配置参数，如果不存在则使用默认值
        can_log_interval_sec_ = config["log_rotation_interval"].as<int>(60);
        can_log_retention_days_ = config["log_retention_days"].as<int>(7);
        enable_logging_ = config["enable_logging"].as<bool>(true);
        enable_real_time_flush_ = config["enable_real_time_flush"].as<bool>(true);
        max_log_file_size_mb_ = config["max_log_file_size_mb"].as<int>(100);
        log_file_prefix_ = config["log_file_prefix"].as<std::string>("can_data");

        // 读取CAN ID列表
        if (config["can_ids_to_log"] && config["can_ids_to_log"].IsSequence())
        {
            can_ids_to_log_.clear();
            for (const auto &id : config["can_ids_to_log"])
            {
                can_ids_to_log_.push_back(id.as<uint32_t>());
            }
        }

        if (config["can_ids_to_filter"] && config["can_ids_to_filter"].IsSequence())
        {
            can_ids_to_filter_.clear();
            for (const auto &id : config["can_ids_to_filter"])
            {
                can_ids_to_filter_.push_back(id.as<uint32_t>());
            }
        }

        ROS_INFO("CAN logging configuration loaded successfully");
        ROS_INFO("  - Logging enabled: %s", enable_logging_ ? "true" : "false");
        ROS_INFO("  - Log rotation interval: %d seconds", can_log_interval_sec_);
        ROS_INFO("  - Log retention: %d days", can_log_retention_days_);
        ROS_INFO("  - Real-time flush: %s", enable_real_time_flush_ ? "true" : "false");
        ROS_INFO("  - Max file size: %d MB", max_log_file_size_mb_);
        ROS_INFO("  - File prefix: %s", log_file_prefix_.c_str());

        if (!can_ids_to_log_.empty())
        {
            ROS_INFO("  - Logging specific CAN IDs: %zu IDs", can_ids_to_log_.size());
        }
        else
        {
            ROS_INFO("  - Logging all CAN IDs");
        }

        if (!can_ids_to_filter_.empty())
        {
            ROS_INFO("  - Filtering out %zu CAN IDs", can_ids_to_filter_.size());
        }
    }
    catch (const std::exception &e)
    {
        ROS_ERROR("Failed to load CAN logging config: %s, using default config", e.what());
        // 使用默认配置
        can_log_interval_sec_ = 60;
        can_log_retention_days_ = 7;
        enable_logging_ = true;
        enable_real_time_flush_ = true;
        max_log_file_size_mb_ = 100;
        log_file_prefix_ = "can_data";
    }
}

void VehicleController::initCanLogging()
{
    try
    {
        // 获取用户主目录
        const char *home_dir = std::getenv("HOME");
        if (home_dir == nullptr)
        {
            ROS_ERROR("Failed to get HOME directory");
            return;
        }

        can_log_directory_ = std::string(home_dir) + "/can_logs";

        // 创建日志目录
        std::filesystem::create_directories(can_log_directory_);

        // 启动时先清理一次旧文件
        cleanupOldLogFiles();

        // 创建初始日志文件
        createNewLogFile();

        ROS_INFO("CAN logging initialized. Log directory: %s", can_log_directory_.c_str());
    }
    catch (const std::exception &e)
    {
        ROS_ERROR("Failed to initialize CAN logging: %s", e.what());
    }
}

void VehicleController::logCanDataToFile(const VCI_CAN_OBJ &can_obj)
{
    std::lock_guard<std::mutex> lock(can_log_mutex_);

    if (!current_log_file_.is_open())
    {
        return;
    }

    try
    {
        // 检查文件大小，如果超过限制则切换到新文件
        if (checkAndRotateLogFileBySize())
        {
            // 文件已切换，确保新文件已打开
            if (!current_log_file_.is_open())
            {
                return;
            }
        }

        // 获取当前时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now.time_since_epoch()) %
                  1000;

        // 格式化时间戳
        std::stringstream timestamp_ss;
        timestamp_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        timestamp_ss << "." << std::setfill('0') << std::setw(3) << ms.count();

        // 格式化CAN数据
        std::stringstream data_ss;
        data_ss << "0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_obj.ID << ": ";
        for (int i = 0; i < can_obj.DataLen; ++i)
        {
            data_ss << std::setfill('0') << std::setw(2) << std::hex << std::uppercase
                    << static_cast<int>(can_obj.Data[i]);
            if (i < can_obj.DataLen - 1)
                data_ss << " ";
        }

        // 写入日志文件
        current_log_file_ << "[" << timestamp_ss.str() << "] " << data_ss.str() << std::endl;

        // 根据配置决定是否立即刷新到磁盘
        if (enable_real_time_flush_)
        {
            current_log_file_.flush();
        }
    }
    catch (const std::exception &e)
    {
        ROS_ERROR("Failed to log CAN data: %s", e.what());
    }
}

void VehicleController::createNewLogFile()
{
    std::lock_guard<std::mutex> lock(can_log_mutex_);

    try
    {
        // 关闭当前文件
        if (current_log_file_.is_open())
        {
            current_log_file_.close();
        }

        // 生成新的文件名
        current_log_filename_ = generateLogFileName();
        std::string full_path = can_log_directory_ + "/" + current_log_filename_;

        // 打开新文件
        current_log_file_.open(full_path, std::ios::out | std::ios::app);
        if (!current_log_file_.is_open())
        {
            ROS_ERROR("Failed to create CAN log file: %s", full_path.c_str());
            return;
        }

        // 写入文件头信息
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        current_log_file_ << "# CAN Data Log File\n";
        current_log_file_ << "# Created: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
        current_log_file_ << "# Format: [YYYY-MM-DD HH:MM:SS.mmm] CAN_ID: DATA\n";
        current_log_file_ << "# ==========================================\n\n";
        current_log_file_.flush();

        ROS_INFO("Created new CAN log file: %s", current_log_filename_.c_str());
    }
    catch (const std::exception &e)
    {
        ROS_ERROR("Failed to create new CAN log file: %s", e.what());
    }
}

void VehicleController::rotateLogFile(const ros::TimerEvent &event)
{
    // createNewLogFile();
}

void VehicleController::cleanupOldLogFiles()
{
    try
    {
        auto now = std::chrono::system_clock::now();
        auto cutoff_time = now - std::chrono::hours(24 * can_log_retention_days_);

        int deleted_count = 0;

        // 遍历日志目录中的所有文件
        for (const auto &entry : std::filesystem::directory_iterator(can_log_directory_))
        {
            if (entry.is_regular_file())
            {
                auto file_time = std::filesystem::last_write_time(entry);

                // 转换文件时间为system_clock时间点
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    file_time - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());

                // 如果文件超过保留期限，删除它
                if (sctp < cutoff_time)
                {
                    std::string filename = entry.path().filename().string();

                    // 确保不删除当前正在使用的日志文件
                    if (filename != current_log_filename_)
                    {
                        std::filesystem::remove(entry.path());
                        deleted_count++;
                        ROS_INFO("Deleted old CAN log file: %s", filename.c_str());
                    }
                }
            }
        }

        if (deleted_count > 0)
        {
            ROS_INFO("CAN log cleanup completed. Deleted %d old files.", deleted_count);
        }
    }
    catch (const std::exception &e)
    {
        ROS_ERROR("Failed to cleanup old CAN log files: %s", e.what());
    }
}

std::string VehicleController::generateLogFileName() const
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream filename_ss;
    filename_ss << log_file_prefix_ << "_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S") << ".log";

    return filename_ss.str();
}

void VehicleController::closeCurrentLogFile()
{
    std::lock_guard<std::mutex> lock(can_log_mutex_);

    if (current_log_file_.is_open())
    {
        // 写入文件结束标记
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        current_log_file_ << "\n# Log file closed: "
                          << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
        current_log_file_.close();
        ROS_INFO("Closed CAN log file: %s", current_log_filename_.c_str());
    }
}

bool VehicleController::shouldLogCanId(uint32_t can_id) const
{
    // 如果在过滤列表中，不记录
    if (!can_ids_to_filter_.empty())
    {
        if (std::find(can_ids_to_filter_.begin(), can_ids_to_filter_.end(), can_id) != can_ids_to_filter_.end())
        {
            return false;
        }
    }

    // 如果指定了要记录的ID列表，且不为空，则只记录列表中的ID
    if (!can_ids_to_log_.empty())
    {
        return std::find(can_ids_to_log_.begin(), can_ids_to_log_.end(), can_id) != can_ids_to_log_.end();
    }

    // 默认记录所有ID
    return true;
}

size_t VehicleController::getCurrentLogFileSize() const
{
    if (!current_log_file_.is_open() || current_log_filename_.empty())
    {
        return 0;
    }

    try
    {
        std::string full_path = can_log_directory_ + "/" + current_log_filename_;
        if (std::filesystem::exists(full_path))
        {
            return std::filesystem::file_size(full_path);
        }
    }
    catch (const std::exception &e)
    {
        ROS_WARN("Failed to get log file size: %s", e.what());
    }

    return 0;
}

bool VehicleController::checkAndRotateLogFileBySize()
{
    // 获取当前文件大小(字节)
    size_t current_size_bytes = getCurrentLogFileSize();

    // 转换为MB
    double current_size_mb = static_cast<double>(current_size_bytes) / (1024.0 * 1024.0);

    // 检查是否超过大小限制
    if (current_size_mb >= static_cast<double>(max_log_file_size_mb_))
    {
        ROS_INFO("Log file size (%.2f MB) exceeded limit (%d MB), rotating to new file",
                 current_size_mb, max_log_file_size_mb_);

        // 关闭当前文件并创建新文件
        if (current_log_file_.is_open())
        {
            // 写入文件结束标记
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            current_log_file_ << "\n# Log file rotated due to size limit: "
                              << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
            current_log_file_.close();
        }

        // 创建新的日志文件
        createNewLogFile();

        return true; // 表示文件已切换
    }

    return false; // 表示文件未切换
}

void VehicleController::ForceReverseCallback(const std_msgs::Bool::ConstPtr &msg)
{
    if (msg->data && !reverse_module_.isReversing())
    {
        // 只有在当前不在倒车模式且收到强制倒车请求时才处理
        ROS_WARN("Received force reverse request from obstacle detector!");

        // 获取当前位置作为倒车调整的目标点
        // 这里使用当前目标点位置，因为我们只是想让车辆转向避开障碍物
        double current_x = goal.pose.position.x;  // 使用当前目标点位置
        double current_y = goal.pose.position.y;

        // 模拟一个当前位姿（在实际应用中应该从定位系统获取）
        geometry_msgs::Pose current_pose;
        current_pose.position.x = current_x;
        current_pose.position.y = current_y;
        current_pose.position.z = 0.0;
        current_pose.orientation.w = 1.0;  // 默认朝向

        // 开始倒车调整（强制倒车时使用当前目标点的方向，如果没有则默认left）
        reverse_module_.startReverseAdjustment(current_pose, current_x, current_y, current_goal_reverse_direction);

        // 发布倒车状态
        std_msgs::Bool reverse_status_msg;
        reverse_status_msg.data = true;
        reverse_status_pub.publish(reverse_status_msg);

        ROS_WARN("Force reverse adjustment started due to persistent front obstacle!");
    }
}

bool VehicleController::checkCanDeviceStatus()
{
    // 检查CAN设备是否正常工作
    // 这里可以添加具体的设备状态检查逻辑
    // 例如检查设备连接状态、发送测试消息等

    static auto last_check_time = std::chrono::steady_clock::now();
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_check_time);

    // 每10秒检查一次
    if (elapsed.count() >= 10) {
        printf("\n🔍 CAN Device Status Check\n");
        fflush(stdout);
        ROS_INFO("Performing CAN device status check");
        last_check_time = current_time;

        // 这里可以添加具体的设备检查逻辑
        // 目前返回true表示设备正常
        return true;
    }

    return true;
}